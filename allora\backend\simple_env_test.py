#!/usr/bin/env python3
"""
Simple Environment Configuration Test for Allora Backend
========================================================

This script performs basic validation of the .env file configuration.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def test_result(test_name, status, message=""):
    """Print test result"""
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name:<40} [{status}]")
    if message:
        print(f"   {message}")

def main():
    """Main test function"""
    print("🔧 Allora Backend Environment Configuration Test")
    print("=" * 60)
    
    # Test 1: Required Environment Variables
    print_header("REQUIRED ENVIRONMENT VARIABLES")
    
    required_vars = {
        'SECRET_KEY': 'Application secret key',
        'JWT_SECRET_KEY': 'JWT token signing key', 
        'DATABASE_URL': 'Database connection string',
        'FLASK_ENV': 'Flask environment',
        'REDIS_HOST': 'Redis server host',
        'REDIS_PORT': 'Redis server port',
        'MAIL_USERNAME': 'Email username',
        'MAIL_PASSWORD': 'Email password',
        'GOOGLE_CLIENT_ID': 'Google OAuth client ID',
        'GOOGLE_CLIENT_SECRET': 'Google OAuth secret'
    }
    
    missing_vars = []
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values for display
            if any(sensitive in var for sensitive in ['SECRET', 'PASSWORD', 'KEY']):
                display_value = '*' * min(len(value), 20)
            else:
                display_value = value[:50] + '...' if len(value) > 50 else value
            test_result(var, "PASS", f"Value: {display_value}")
        else:
            test_result(var, "FAIL", f"Missing: {desc}")
            missing_vars.append(var)
    
    # Test 2: Optional Variables
    print_header("OPTIONAL ENVIRONMENT VARIABLES")
    
    optional_vars = {
        'ELASTICSEARCH_ENABLED': 'Enable search functionality',
        'ELASTICSEARCH_HOST': 'Elasticsearch server',
        'RAZORPAY_KEY_ID': 'Payment gateway key',
        'SMS_API_KEY': 'SMS service key',
        'SENTRY_DSN': 'Error monitoring',
        'ENABLE_RECOMMENDATIONS': 'ML recommendations',
        'ENABLE_ANALYTICS': 'Analytics tracking'
    }
    
    for var, desc in optional_vars.items():
        value = os.getenv(var)
        if value:
            if 'SECRET' in var or 'KEY' in var:
                display_value = '*' * min(len(value), 15)
            else:
                display_value = value
            test_result(var, "PASS", f"Value: {display_value}")
        else:
            test_result(var, "WARN", f"Not set: {desc}")
    
    # Test 3: Configuration Validation
    print_header("CONFIGURATION VALIDATION")
    
    # Check secret key length
    secret_key = os.getenv('SECRET_KEY', '')
    if len(secret_key) >= 32:
        test_result("Secret Key Length", "PASS", f"{len(secret_key)} characters")
    else:
        test_result("Secret Key Length", "FAIL", f"{len(secret_key)} characters - too short")
        missing_vars.append("SECRET_KEY (length)")
    
    # Check JWT secret length
    jwt_secret = os.getenv('JWT_SECRET_KEY', '')
    if len(jwt_secret) >= 32:
        test_result("JWT Secret Length", "PASS", f"{len(jwt_secret)} characters")
    else:
        test_result("JWT Secret Length", "FAIL", f"{len(jwt_secret)} characters - too short")
        missing_vars.append("JWT_SECRET_KEY (length)")
    
    # Check database URL format
    db_url = os.getenv('DATABASE_URL', '')
    if 'mysql' in db_url and '@' in db_url:
        test_result("Database URL Format", "PASS", "MySQL connection string detected")
    elif db_url:
        test_result("Database URL Format", "WARN", "Non-MySQL database detected")
    else:
        test_result("Database URL Format", "FAIL", "No database URL configured")
    
    # Check Flask environment
    flask_env = os.getenv('FLASK_ENV', 'development')
    if flask_env in ['development', 'production', 'testing']:
        test_result("Flask Environment", "PASS", f"Environment: {flask_env}")
    else:
        test_result("Flask Environment", "WARN", f"Unknown environment: {flask_env}")
    
    # Test 4: Feature Flags
    print_header("FEATURE FLAGS")
    
    feature_flags = {
        'ENABLE_RECOMMENDATIONS': 'ML Recommendations',
        'ENABLE_ANALYTICS': 'Analytics Tracking', 
        'ENABLE_NOTIFICATIONS': 'Notifications',
        'ENABLE_MULTI_VENDOR': 'Multi-vendor Marketplace'
    }
    
    for flag, desc in feature_flags.items():
        value = os.getenv(flag, 'false').lower()
        status = "PASS" if value == 'true' else "INFO"
        test_result(desc, status, f"Enabled: {value}")
    
    # Test 5: Security Settings
    print_header("SECURITY SETTINGS")
    
    # HTTPS redirect
    https_redirect = os.getenv('HTTPS_REDIRECT', 'false').lower()
    if flask_env == 'production' and https_redirect != 'true':
        test_result("HTTPS Redirect", "WARN", "Should be enabled in production")
    else:
        test_result("HTTPS Redirect", "PASS", f"Setting: {https_redirect}")
    
    # Rate limiting
    rate_limit = os.getenv('RATE_LIMIT_ENABLED', 'true').lower()
    test_result("Rate Limiting", "PASS" if rate_limit == 'true' else "WARN", f"Enabled: {rate_limit}")
    
    # CORS origins
    cors_origins = os.getenv('CORS_ORIGINS', '')
    if cors_origins:
        test_result("CORS Origins", "PASS", f"Origins: {cors_origins}")
    else:
        test_result("CORS Origins", "WARN", "No CORS origins configured")
    
    # Summary
    print_header("SUMMARY")
    
    if missing_vars:
        print("❌ CRITICAL ISSUES FOUND:")
        for var in missing_vars:
            print(f"   - {var}")
        print(f"\n⚠️  Please fix {len(missing_vars)} critical issue(s) before running the application.")
        return False
    else:
        print("✅ All critical environment variables are properly configured!")
        print("🎉 Your .env file is ready for the Allora backend application.")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

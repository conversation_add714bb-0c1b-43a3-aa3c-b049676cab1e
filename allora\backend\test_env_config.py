#!/usr/bin/env python3
"""
Environment Configuration Validator for Allora Backend
=====================================================

This script validates that all required environment variables are properly
configured in the .env file and tests their functionality.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import sys
import re
import json
from typing import Dict, List, Tuple, Any, Optional
from dotenv import load_dotenv
import mysql.connector
import redis
import requests
from urllib.parse import urlparse

# Load environment variables
load_dotenv()

class Colors:
    """ANSI color codes for terminal output"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class EnvValidator:
    """Environment configuration validator"""
    
    def __init__(self):
        self.results = []
        self.critical_failures = []
        self.warnings = []
        
    def print_header(self, title: str):
        """Print section header"""
        print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{title.center(60)}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
    
    def print_result(self, test_name: str, status: str, message: str = "", critical: bool = False):
        """Print test result with color coding"""
        if status == "PASS":
            icon = f"{Colors.GREEN}✅{Colors.END}"
            status_color = f"{Colors.GREEN}PASS{Colors.END}"
        elif status == "FAIL":
            icon = f"{Colors.RED}❌{Colors.END}"
            status_color = f"{Colors.RED}FAIL{Colors.END}"
            if critical:
                self.critical_failures.append(test_name)
        elif status == "WARN":
            icon = f"{Colors.YELLOW}⚠️{Colors.END}"
            status_color = f"{Colors.YELLOW}WARN{Colors.END}"
            self.warnings.append(test_name)
        else:
            icon = f"{Colors.CYAN}ℹ️{Colors.END}"
            status_color = f"{Colors.CYAN}INFO{Colors.END}"
        
        print(f"{icon} {test_name:<40} [{status_color}]")
        if message:
            print(f"   {Colors.WHITE}{message}{Colors.END}")
        
        self.results.append({
            'test': test_name,
            'status': status,
            'message': message,
            'critical': critical
        })

    def validate_required_vars(self) -> None:
        """Validate that all required environment variables are present"""
        self.print_header("REQUIRED ENVIRONMENT VARIABLES")
        
        required_vars = {
            # Core Application
            'SECRET_KEY': 'Application secret key for security',
            'JWT_SECRET_KEY': 'JWT token signing key',
            'FLASK_ENV': 'Flask environment (development/production)',
            
            # Database
            'DATABASE_URL': 'Primary database connection string',
            
            # Redis
            'REDIS_HOST': 'Redis server host',
            'REDIS_PORT': 'Redis server port',
            
            # Email
            'MAIL_USERNAME': 'Email service username',
            'MAIL_PASSWORD': 'Email service password',
            
            # OAuth
            'GOOGLE_CLIENT_ID': 'Google OAuth client ID',
            'GOOGLE_CLIENT_SECRET': 'Google OAuth client secret',
        }
        
        for var, description in required_vars.items():
            value = os.getenv(var)
            if value:
                # Mask sensitive values
                display_value = value if var not in ['SECRET_KEY', 'JWT_SECRET_KEY', 'MAIL_PASSWORD', 'GOOGLE_CLIENT_SECRET'] else '*' * 20
                self.print_result(f"{var}", "PASS", f"Value: {display_value}")
            else:
                self.print_result(f"{var}", "FAIL", f"Missing: {description}", critical=True)

    def validate_optional_vars(self) -> None:
        """Validate optional environment variables"""
        self.print_header("OPTIONAL ENVIRONMENT VARIABLES")
        
        optional_vars = {
            # Elasticsearch
            'ELASTICSEARCH_ENABLED': 'Enable Elasticsearch search',
            'ELASTICSEARCH_HOST': 'Elasticsearch server host',
            'ELASTICSEARCH_PORT': 'Elasticsearch server port',
            
            # Payment Gateways
            'RAZORPAY_KEY_ID': 'Razorpay payment gateway key',
            'RAZORPAY_KEY_SECRET': 'Razorpay payment gateway secret',
            
            # SMS
            'SMS_API_KEY': 'SMS service API key',
            'SMS_SENDER_ID': 'SMS sender ID',
            
            # Monitoring
            'SENTRY_DSN': 'Sentry error monitoring DSN',
            
            # Shipping Carriers
            'FEDEX_API_KEY': 'FedEx shipping API key',
            'UPS_API_KEY': 'UPS shipping API key',
            'DHL_API_KEY': 'DHL shipping API key',
            'BLUE_DART_API_KEY': 'Blue Dart shipping API key',
            'DELHIVERY_API_KEY': 'Delhivery shipping API key',
            
            # Feature Flags
            'ENABLE_RECOMMENDATIONS': 'Enable ML recommendations',
            'ENABLE_ANALYTICS': 'Enable analytics tracking',
            'ENABLE_NOTIFICATIONS': 'Enable notifications',
            'ENABLE_MULTI_VENDOR': 'Enable multi-vendor marketplace',
        }
        
        for var, description in optional_vars.items():
            value = os.getenv(var)
            if value:
                # Mask sensitive values
                display_value = value if 'SECRET' not in var and 'KEY' not in var and 'PASSWORD' not in var else '*' * 15
                self.print_result(f"{var}", "PASS", f"Value: {display_value}")
            else:
                self.print_result(f"{var}", "WARN", f"Not set: {description}")

    def test_database_connection(self) -> None:
        """Test database connectivity"""
        self.print_header("DATABASE CONNECTION TEST")
        
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            self.print_result("Database URL", "FAIL", "DATABASE_URL not configured", critical=True)
            return
        
        try:
            # Parse MySQL connection string
            if database_url.startswith('mysql'):
                # Extract connection details
                pattern = r'mysql\+mysqlconnector://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)'
                match = re.match(pattern, database_url)
                
                if match:
                    username, password, host, port, database = match.groups()
                    
                    # Test connection
                    conn = mysql.connector.connect(
                        host=host,
                        port=int(port),
                        user=username,
                        password=password,
                        database=database,
                        connection_timeout=10
                    )
                    
                    cursor = conn.cursor()
                    cursor.execute("SELECT VERSION()")
                    version = cursor.fetchone()[0]
                    
                    cursor.close()
                    conn.close()
                    
                    self.print_result("Database Connection", "PASS", f"MySQL {version}")
                    self.print_result("Database Host", "PASS", f"{host}:{port}")
                    self.print_result("Database Name", "PASS", database)
                else:
                    self.print_result("Database URL Format", "FAIL", "Invalid MySQL URL format", critical=True)
            else:
                self.print_result("Database Type", "WARN", "Non-MySQL database detected")
                
        except Exception as e:
            self.print_result("Database Connection", "FAIL", f"Connection failed: {str(e)}", critical=True)

    def test_redis_connection(self) -> None:
        """Test Redis connectivity"""
        self.print_header("REDIS CONNECTION TEST")
        
        redis_host = os.getenv('REDIS_HOST', 'localhost')
        redis_port = int(os.getenv('REDIS_PORT', '6379'))
        redis_password = os.getenv('REDIS_PASSWORD')
        
        try:
            # Test main Redis connection
            r = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                decode_responses=True,
                socket_connect_timeout=5
            )
            
            # Test ping
            r.ping()
            self.print_result("Redis Connection", "PASS", f"{redis_host}:{redis_port}")
            
            # Test Redis info
            info = r.info()
            self.print_result("Redis Version", "PASS", info.get('redis_version', 'Unknown'))
            self.print_result("Redis Memory", "PASS", f"{info.get('used_memory_human', 'Unknown')}")
            
            # Test different Redis databases
            redis_dbs = {
                'SESSION_REDIS': 'Session storage',
                'CACHE_REDIS': 'Application cache',
                'RATE_LIMIT_STORAGE_URL': 'Rate limiting'
            }
            
            for db_var, description in redis_dbs.items():
                db_url = os.getenv(db_var)
                if db_url:
                    try:
                        db_client = redis.from_url(db_url, decode_responses=True, socket_connect_timeout=5)
                        db_client.ping()
                        self.print_result(f"Redis {description}", "PASS", f"DB connection OK")
                    except Exception as e:
                        self.print_result(f"Redis {description}", "FAIL", f"Connection failed: {str(e)}")
                else:
                    self.print_result(f"Redis {description}", "WARN", "URL not configured")
            
        except Exception as e:
            self.print_result("Redis Connection", "FAIL", f"Connection failed: {str(e)}", critical=True)

    def test_elasticsearch_connection(self) -> None:
        """Test Elasticsearch connectivity"""
        self.print_header("ELASTICSEARCH CONNECTION TEST")
        
        es_enabled = os.getenv('ELASTICSEARCH_ENABLED', 'true').lower() == 'true'
        
        if not es_enabled:
            self.print_result("Elasticsearch", "INFO", "Disabled in configuration")
            return
        
        es_host = os.getenv('ELASTICSEARCH_HOST', 'localhost')
        es_port = os.getenv('ELASTICSEARCH_PORT', '9200')
        es_scheme = os.getenv('ELASTICSEARCH_SCHEME', 'http')
        
        es_url = f"{es_scheme}://{es_host}:{es_port}"
        
        try:
            # Test Elasticsearch connection
            response = requests.get(f"{es_url}/_cluster/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                self.print_result("Elasticsearch Connection", "PASS", f"{es_url}")
                self.print_result("Elasticsearch Status", "PASS", health_data.get('status', 'unknown'))
                self.print_result("Elasticsearch Nodes", "PASS", f"{health_data.get('number_of_nodes', 0)} nodes")
            else:
                self.print_result("Elasticsearch Connection", "FAIL", f"HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            self.print_result("Elasticsearch Connection", "FAIL", "Connection refused - service may be down")
        except Exception as e:
            self.print_result("Elasticsearch Connection", "FAIL", f"Error: {str(e)}")

    def test_email_configuration(self) -> None:
        """Test email configuration"""
        self.print_header("EMAIL CONFIGURATION TEST")
        
        mail_server = os.getenv('MAIL_SERVER')
        mail_port = os.getenv('MAIL_PORT')
        mail_username = os.getenv('MAIL_USERNAME')
        mail_password = os.getenv('MAIL_PASSWORD')
        
        if not all([mail_server, mail_port, mail_username, mail_password]):
            self.print_result("Email Configuration", "FAIL", "Missing email configuration", critical=True)
            return
        
        try:
            import smtplib
            from email.mime.text import MIMEText
            
            # Test SMTP connection
            server = smtplib.SMTP(mail_server, int(mail_port))
            server.starttls()
            server.login(mail_username, mail_password)
            server.quit()
            
            self.print_result("SMTP Connection", "PASS", f"{mail_server}:{mail_port}")
            self.print_result("SMTP Authentication", "PASS", f"User: {mail_username}")
            
        except Exception as e:
            self.print_result("SMTP Connection", "FAIL", f"Connection failed: {str(e)}")

    def validate_security_settings(self) -> None:
        """Validate security-related settings"""
        self.print_header("SECURITY VALIDATION")
        
        # Check secret key strength
        secret_key = os.getenv('SECRET_KEY', '')
        if len(secret_key) >= 32:
            self.print_result("Secret Key Length", "PASS", f"{len(secret_key)} characters")
        elif len(secret_key) >= 16:
            self.print_result("Secret Key Length", "WARN", f"{len(secret_key)} characters - consider longer key")
        else:
            self.print_result("Secret Key Length", "FAIL", f"{len(secret_key)} characters - too short", critical=True)
        
        # Check JWT secret key
        jwt_secret = os.getenv('JWT_SECRET_KEY', '')
        if len(jwt_secret) >= 32:
            self.print_result("JWT Secret Length", "PASS", f"{len(jwt_secret)} characters")
        else:
            self.print_result("JWT Secret Length", "FAIL", f"{len(jwt_secret)} characters - too short", critical=True)
        
        # Check HTTPS settings
        https_redirect = os.getenv('HTTPS_REDIRECT', 'false').lower()
        flask_env = os.getenv('FLASK_ENV', 'development')
        
        if flask_env == 'production' and https_redirect != 'true':
            self.print_result("HTTPS Redirect", "WARN", "Should be enabled in production")
        else:
            self.print_result("HTTPS Redirect", "PASS", f"Set to {https_redirect}")
        
        # Check rate limiting
        rate_limit_enabled = os.getenv('RATE_LIMIT_ENABLED', 'true').lower()
        self.print_result("Rate Limiting", "PASS" if rate_limit_enabled == 'true' else "WARN", 
                         f"Enabled: {rate_limit_enabled}")

    def validate_feature_flags(self) -> None:
        """Validate feature flag settings"""
        self.print_header("FEATURE FLAGS VALIDATION")
        
        feature_flags = {
            'ENABLE_RECOMMENDATIONS': 'ML Recommendations',
            'ENABLE_ANALYTICS': 'Analytics Tracking',
            'ENABLE_NOTIFICATIONS': 'Notifications',
            'ENABLE_MULTI_VENDOR': 'Multi-vendor Marketplace',
            'ENABLE_INVENTORY_SYNC': 'Inventory Synchronization'
        }
        
        for flag, description in feature_flags.items():
            value = os.getenv(flag, 'false').lower()
            status = "PASS" if value == 'true' else "INFO"
            self.print_result(f"{description}", status, f"Enabled: {value}")

    def generate_summary(self) -> None:
        """Generate validation summary"""
        self.print_header("VALIDATION SUMMARY")
        
        total_tests = len(self.results)
        passed = len([r for r in self.results if r['status'] == 'PASS'])
        failed = len([r for r in self.results if r['status'] == 'FAIL'])
        warnings = len([r for r in self.results if r['status'] == 'WARN'])
        
        print(f"{Colors.BOLD}Total Tests:{Colors.END} {total_tests}")
        print(f"{Colors.GREEN}Passed:{Colors.END} {passed}")
        print(f"{Colors.RED}Failed:{Colors.END} {failed}")
        print(f"{Colors.YELLOW}Warnings:{Colors.END} {warnings}")
        
        if self.critical_failures:
            print(f"\n{Colors.RED}{Colors.BOLD}CRITICAL FAILURES:{Colors.END}")
            for failure in self.critical_failures:
                print(f"{Colors.RED}  ❌ {failure}{Colors.END}")
        
        if failed == 0:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 All critical tests passed! Your .env configuration is ready.{Colors.END}")
        else:
            print(f"\n{Colors.RED}{Colors.BOLD}⚠️  Please fix the critical failures before running the application.{Colors.END}")
        
        return failed == 0

def main():
    """Main validation function"""
    print(f"{Colors.BOLD}{Colors.PURPLE}")
    print("🔧 Allora Backend Environment Configuration Validator")
    print("=" * 60)
    print(f"{Colors.END}")
    
    validator = EnvValidator()
    
    # Run all validation tests
    validator.validate_required_vars()
    validator.validate_optional_vars()
    validator.test_database_connection()
    validator.test_redis_connection()
    validator.test_elasticsearch_connection()
    validator.test_email_configuration()
    validator.validate_security_settings()
    validator.validate_feature_flags()
    
    # Generate summary
    success = validator.generate_summary()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

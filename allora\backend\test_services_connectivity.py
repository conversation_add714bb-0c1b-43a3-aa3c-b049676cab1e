#!/usr/bin/env python3
"""
Service Connectivity Test for Allora Backend
============================================

This script tests actual connectivity to external services configured in .env

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import sys
import re
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def test_result(test_name, status, message=""):
    """Print test result"""
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name:<40} [{status}]")
    if message:
        print(f"   {message}")

def test_database_connection():
    """Test database connectivity"""
    print_header("DATABASE CONNECTION TEST")
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        test_result("Database Configuration", "FAIL", "DATABASE_URL not set")
        return False
    
    try:
        import mysql.connector
        
        # Parse MySQL connection string
        pattern = r'mysql\+mysqlconnector://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)'
        match = re.match(pattern, database_url)
        
        if not match:
            test_result("Database URL Format", "FAIL", "Invalid MySQL URL format")
            return False
        
        username, password, host, port, database = match.groups()
        
        # Test connection
        conn = mysql.connector.connect(
            host=host,
            port=int(port),
            user=username,
            password=password,
            database=database,
            connection_timeout=10
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        
        # Test if we can access tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        test_result("Database Connection", "PASS", f"MySQL {version}")
        test_result("Database Access", "PASS", f"Found {len(tables)} tables")
        test_result("Database Host", "PASS", f"{host}:{port}")
        test_result("Database Name", "PASS", database)
        
        return True
        
    except ImportError:
        test_result("MySQL Connector", "FAIL", "mysql-connector-python not installed")
        return False
    except Exception as e:
        test_result("Database Connection", "FAIL", f"Error: {str(e)}")
        return False

def test_redis_connection():
    """Test Redis connectivity"""
    print_header("REDIS CONNECTION TEST")
    
    redis_host = os.getenv('REDIS_HOST', 'localhost')
    redis_port = int(os.getenv('REDIS_PORT', '6379'))
    redis_password = os.getenv('REDIS_PASSWORD')
    
    try:
        import redis
        
        # Test main Redis connection
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True,
            socket_connect_timeout=5
        )
        
        # Test ping
        r.ping()
        test_result("Redis Connection", "PASS", f"{redis_host}:{redis_port}")
        
        # Test basic operations
        r.set('test_key', 'test_value', ex=10)
        value = r.get('test_key')
        r.delete('test_key')
        
        if value == 'test_value':
            test_result("Redis Operations", "PASS", "Read/Write operations working")
        else:
            test_result("Redis Operations", "FAIL", "Read/Write operations failed")
        
        # Test Redis info
        info = r.info()
        test_result("Redis Version", "PASS", info.get('redis_version', 'Unknown'))
        test_result("Redis Memory Usage", "PASS", info.get('used_memory_human', 'Unknown'))
        
        return True
        
    except ImportError:
        test_result("Redis Library", "FAIL", "redis library not installed")
        return False
    except Exception as e:
        test_result("Redis Connection", "FAIL", f"Error: {str(e)}")
        return False

def test_elasticsearch_connection():
    """Test Elasticsearch connectivity"""
    print_header("ELASTICSEARCH CONNECTION TEST")
    
    es_enabled = os.getenv('ELASTICSEARCH_ENABLED', 'true').lower() == 'true'
    
    if not es_enabled:
        test_result("Elasticsearch", "INFO", "Disabled in configuration")
        return True
    
    es_host = os.getenv('ELASTICSEARCH_HOST', 'localhost')
    es_port = os.getenv('ELASTICSEARCH_PORT', '9200')
    es_scheme = os.getenv('ELASTICSEARCH_SCHEME', 'http')
    
    es_url = f"{es_scheme}://{es_host}:{es_port}"
    
    try:
        import requests
        
        # Test Elasticsearch connection
        response = requests.get(f"{es_url}/_cluster/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            test_result("Elasticsearch Connection", "PASS", f"{es_url}")
            test_result("Elasticsearch Status", "PASS", health_data.get('status', 'unknown'))
            test_result("Elasticsearch Nodes", "PASS", f"{health_data.get('number_of_nodes', 0)} nodes")
            
            # Test if we can create/delete a test index
            test_index = "allora_test_index"
            create_response = requests.put(f"{es_url}/{test_index}", timeout=5)
            delete_response = requests.delete(f"{es_url}/{test_index}", timeout=5)
            
            if create_response.status_code in [200, 201] and delete_response.status_code == 200:
                test_result("Elasticsearch Operations", "PASS", "Index create/delete working")
            else:
                test_result("Elasticsearch Operations", "WARN", "Limited permissions")
            
            return True
        else:
            test_result("Elasticsearch Connection", "FAIL", f"HTTP {response.status_code}")
            return False
            
    except ImportError:
        test_result("Requests Library", "FAIL", "requests library not installed")
        return False
    except requests.exceptions.ConnectionError:
        test_result("Elasticsearch Connection", "FAIL", "Connection refused - service may be down")
        return False
    except Exception as e:
        test_result("Elasticsearch Connection", "FAIL", f"Error: {str(e)}")
        return False

def test_email_configuration():
    """Test email configuration"""
    print_header("EMAIL CONFIGURATION TEST")
    
    mail_server = os.getenv('MAIL_SERVER')
    mail_port = os.getenv('MAIL_PORT')
    mail_username = os.getenv('MAIL_USERNAME')
    mail_password = os.getenv('MAIL_PASSWORD')
    
    if not all([mail_server, mail_port, mail_username, mail_password]):
        test_result("Email Configuration", "FAIL", "Missing email configuration")
        return False
    
    try:
        import smtplib
        
        # Test SMTP connection
        server = smtplib.SMTP(mail_server, int(mail_port))
        server.starttls()
        server.login(mail_username, mail_password)
        server.quit()
        
        test_result("SMTP Connection", "PASS", f"{mail_server}:{mail_port}")
        test_result("SMTP Authentication", "PASS", f"User: {mail_username}")
        
        return True
        
    except Exception as e:
        test_result("SMTP Connection", "FAIL", f"Error: {str(e)}")
        return False

def test_external_apis():
    """Test external API configurations"""
    print_header("EXTERNAL API CONFIGURATION TEST")
    
    # Test payment gateway configuration
    razorpay_key = os.getenv('RAZORPAY_KEY_ID')
    razorpay_secret = os.getenv('RAZORPAY_KEY_SECRET')
    
    if razorpay_key and razorpay_secret:
        test_result("Razorpay Configuration", "PASS", f"Key ID: {razorpay_key[:10]}...")
    else:
        test_result("Razorpay Configuration", "WARN", "Payment gateway not configured")
    
    # Test SMS API configuration
    sms_api_key = os.getenv('SMS_API_KEY')
    sms_sender_id = os.getenv('SMS_SENDER_ID')
    
    if sms_api_key:
        test_result("SMS API Configuration", "PASS", f"Sender ID: {sms_sender_id}")
    else:
        test_result("SMS API Configuration", "WARN", "SMS service not configured")
    
    # Test OAuth configuration
    google_client_id = os.getenv('GOOGLE_CLIENT_ID')
    google_client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
    
    if google_client_id and google_client_secret:
        test_result("Google OAuth Configuration", "PASS", f"Client ID: {google_client_id[:20]}...")
    else:
        test_result("Google OAuth Configuration", "FAIL", "Google OAuth not configured")
    
    # Test monitoring configuration
    sentry_dsn = os.getenv('SENTRY_DSN')
    if sentry_dsn:
        test_result("Sentry Monitoring", "PASS", "Error monitoring configured")
    else:
        test_result("Sentry Monitoring", "WARN", "Error monitoring not configured")
    
    return True

def main():
    """Main test function"""
    print("🔧 Allora Backend Service Connectivity Test")
    print("=" * 60)
    
    results = []
    
    # Run all connectivity tests
    results.append(test_database_connection())
    results.append(test_redis_connection())
    results.append(test_elasticsearch_connection())
    results.append(test_email_configuration())
    results.append(test_external_apis())
    
    # Summary
    print_header("CONNECTIVITY SUMMARY")
    
    passed = sum(results)
    total = len(results)
    
    print(f"Services Tested: {total}")
    print(f"Services Working: {passed}")
    print(f"Services Failed: {total - passed}")
    
    if passed == total:
        print("\n✅ All services are properly configured and accessible!")
        print("🎉 Your Allora backend is ready to run.")
        return True
    else:
        print(f"\n⚠️  {total - passed} service(s) need attention.")
        print("Please check the failed services before running the application.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

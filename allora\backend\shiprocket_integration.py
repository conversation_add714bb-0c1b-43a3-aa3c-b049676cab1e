#!/usr/bin/env python3
"""
Shiprocket API Integration for Allora Backend
============================================

This module provides Shiprocket shipping integration functionality.
"""

import os
import requests
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

load_dotenv()

class ShiprocketAPI:
    """Shiprocket API integration class"""
    
    def __init__(self):
        self.email = os.getenv('SHIPROCKET_EMAIL')
        self.password = os.getenv('SHIPROCKET_PASSWORD')
        self.sandbox = os.getenv('SHIPROCKET_SANDBOX', 'true').lower() == 'true'
        
        if self.sandbox:
            self.base_url = os.getenv('SHIPROCKET_STAGING_URL', 'https://staging-apiv2.shiprocket.in/v1')
        else:
            self.base_url = os.getenv('SHIPROCKET_BASE_URL', 'https://apiv2.shiprocket.in/v1')
        
        self.token = None
        self.token_expires = None
    
    def authenticate(self) -> bool:
        """Authenticate with Shiprocket API"""
        try:
            auth_url = f"{self.base_url}/external/auth/login"
            auth_data = {
                "email": self.email,
                "password": self.password
            }
            
            response = requests.post(auth_url, json=auth_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('token')
                # Token typically expires in 10 days
                return True
            else:
                print(f"Authentication failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return False
    
    def get_headers(self) -> Dict[str, str]:
        """Get API headers with authentication"""
        if not self.token:
            self.authenticate()
        
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}'
        }
    
    def calculate_rates(self, pickup_pincode: str, delivery_pincode: str, 
                       weight: float, cod: bool = False, declared_value: float = 0) -> Dict:
        """Calculate shipping rates"""
        try:
            url = f"{self.base_url}/external/courier/serviceability/"
            
            params = {
                'pickup_postcode': pickup_pincode,
                'delivery_postcode': delivery_pincode,
                'weight': weight,
                'cod': 1 if cod else 0,
                'declared_value': declared_value
            }
            
            response = requests.get(url, params=params, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Rate calculation failed: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Rate calculation error: {str(e)}'}
    
    def create_order(self, order_data: Dict) -> Dict:
        """Create shipping order"""
        try:
            url = f"{self.base_url}/external/orders/create/adhoc"
            
            response = requests.post(url, json=order_data, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Order creation failed: {response.status_code}', 'details': response.text}
                
        except Exception as e:
            return {'error': f'Order creation error: {str(e)}'}
    
    def generate_awb(self, shipment_id: int, courier_id: int) -> Dict:
        """Generate AWB for shipment"""
        try:
            url = f"{self.base_url}/external/courier/assign/awb"
            
            data = {
                'shipment_id': shipment_id,
                'courier_id': courier_id
            }
            
            response = requests.post(url, json=data, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'AWB generation failed: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'AWB generation error: {str(e)}'}
    
    def track_shipment(self, awb_number: str) -> Dict:
        """Track shipment by AWB number"""
        try:
            url = f"{self.base_url}/external/courier/track/awb/{awb_number}"
            
            response = requests.get(url, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Tracking failed: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Tracking error: {str(e)}'}
    
    def schedule_pickup(self, shipment_id: int, pickup_date: str) -> Dict:
        """Schedule pickup for shipment"""
        try:
            url = f"{self.base_url}/external/courier/generate/pickup"
            
            data = {
                'shipment_id': [shipment_id],
                'pickup_date': pickup_date
            }
            
            response = requests.post(url, json=data, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Pickup scheduling failed: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Pickup scheduling error: {str(e)}'}
    
    def cancel_shipment(self, awb_numbers: List[str]) -> Dict:
        """Cancel shipment(s)"""
        try:
            url = f"{self.base_url}/external/orders/cancel"
            
            data = {
                'awbs': awb_numbers
            }
            
            response = requests.post(url, json=data, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Cancellation failed: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Cancellation error: {str(e)}'}
    
    def get_pickup_locations(self) -> Dict:
        """Get configured pickup locations"""
        try:
            url = f"{self.base_url}/external/settings/company/pickup"
            
            response = requests.get(url, headers=self.get_headers(), timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'Failed to get pickup locations: {response.status_code}'}
                
        except Exception as e:
            return {'error': f'Pickup locations error: {str(e)}'}

def format_order_for_shiprocket(order_data: Dict) -> Dict:
    """Format order data for Shiprocket API"""
    
    # Extract required fields
    order_id = order_data.get('order_id')
    customer = order_data.get('customer', {})
    items = order_data.get('items', [])
    shipping_address = order_data.get('shipping_address', {})
    
    # Format items for Shiprocket
    formatted_items = []
    for item in items:
        formatted_items.append({
            "name": item.get('name', ''),
            "sku": item.get('sku', ''),
            "units": item.get('quantity', 1),
            "selling_price": item.get('price', 0),
            "discount": item.get('discount', 0),
            "tax": item.get('tax', 0),
            "hsn": item.get('hsn', 123456)  # Default HSN code
        })
    
    # Calculate totals
    sub_total = sum(item.get('price', 0) * item.get('quantity', 1) for item in items)
    
    # Format for Shiprocket
    shiprocket_order = {
        "order_id": str(order_id),
        "order_date": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "pickup_location": os.getenv('SHIPROCKET_DEFAULT_PICKUP_LOCATION', 'primary'),
        "billing_customer_name": customer.get('name', ''),
        "billing_last_name": customer.get('last_name', ''),
        "billing_address": shipping_address.get('address', ''),
        "billing_address_2": shipping_address.get('address_2', ''),
        "billing_city": shipping_address.get('city', ''),
        "billing_pincode": shipping_address.get('pincode', ''),
        "billing_state": shipping_address.get('state', ''),
        "billing_country": shipping_address.get('country', 'India'),
        "billing_email": customer.get('email', ''),
        "billing_phone": customer.get('phone', ''),
        "shipping_is_billing": True,
        "order_items": formatted_items,
        "payment_method": order_data.get('payment_method', 'COD'),
        "shipping_charges": order_data.get('shipping_charges', 0),
        "giftwrap_charges": 0,
        "transaction_charges": 0,
        "total_discount": order_data.get('discount', 0),
        "sub_total": sub_total,
        "length": order_data.get('package', {}).get('length', 10),
        "breadth": order_data.get('package', {}).get('width', 10),
        "height": order_data.get('package', {}).get('height', 10),
        "weight": order_data.get('package', {}).get('weight', 0.5)
    }
    
    return shiprocket_order

# Example usage
if __name__ == "__main__":
    # Test the integration
    shiprocket = ShiprocketAPI()
    
    # Test authentication
    if shiprocket.authenticate():
        print("✅ Authentication successful")
        
        # Test rate calculation
        rates = shiprocket.calculate_rates('400001', '110001', 1.0, cod=True, declared_value=1000)
        print(f"📦 Rates: {rates}")
        
    else:
        print("❌ Authentication failed")

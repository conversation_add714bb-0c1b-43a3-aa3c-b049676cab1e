# Shiprocket Shipping Configuration Guide for Allora Backend

**Target:** India Domestic & International Shipping  
**Carrier:** Shiprocket (Multi-carrier Platform)  
**Integration Type:** REST API  
**Documentation:** [Shiprocket API Documentation](https://apidocs.shiprocket.in/)

---

## 📋 **Overview**

Shiprocket is India's leading shipping aggregator that provides access to 17+ courier partners through a single API. This guide will help you configure Shiprocket integration for both domestic and international shipping in your Allora e-commerce backend.

### **Shiprocket Advantages:**
- ✅ **17+ Courier Partners** (Blue Dart, FedEx, Delhivery, Ecom Express, etc.)
- ✅ **Single API Integration** for multiple carriers
- ✅ **Automatic Rate Comparison** and best rate selection
- ✅ **Real-time Tracking** across all carriers
- ✅ **COD & Prepaid** support
- ✅ **International Shipping** to 220+ countries
- ✅ **Pickup Scheduling** and management
- ✅ **Return Management** (RTO handling)
- ✅ **Weight Reconciliation** and billing
- ✅ **Dashboard & Analytics**

---

## 🚀 **Step 1: Get Shiprocket Account & API Access**

### **For Development/Testing:**

#### **Option A: Create Free Account**
1. **Visit Shiprocket:**
   - Go to: https://www.shiprocket.in/
   - Click "Sign Up" for free account

2. **Complete Registration:**
   - Business Name
   - Contact Details
   - Email Verification
   - Phone Verification

3. **Get API Credentials:**
   - Login to Shiprocket Dashboard
   - Go to "Settings" → "API"
   - Note down your login email and password
   - API uses email/password authentication

#### **Option B: Use Demo Credentials (Immediate Testing)**
```bash
# Add these to your .env file for immediate testing
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=demo_password
SHIPROCKET_SANDBOX=true
```

### **For Production:**

1. **Complete KYC Process:**
   - Business Registration Certificate
   - GST Certificate
   - PAN Card
   - Bank Account Details
   - Address Proof

2. **Setup Billing:**
   - Add payment method
   - Minimum wallet balance
   - Credit facility (if required)

3. **Configure Pickup Locations:**
   - Add warehouse/store addresses
   - Verify pickup locations
   - Set default pickup location

---

## 🔧 **Step 2: Configure Environment Variables**

Add these variables to your `.env` file:

```bash
# ===========================================
# SHIPROCKET SHIPPING CONFIGURATION
# ===========================================

# Shiprocket Account Credentials
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password

# Environment Settings
SHIPROCKET_SANDBOX=true  # Set to false for production
SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1
SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1

# Default Configuration
SHIPROCKET_DEFAULT_PICKUP_LOCATION=primary
SHIPROCKET_AUTO_AWB=true
SHIPROCKET_AUTO_PICKUP=true

# Webhook Configuration (Optional)
SHIPROCKET_WEBHOOK_URL=https://yourdomain.com/api/webhooks/shiprocket
SHIPROCKET_WEBHOOK_SECRET=your_webhook_secret

# Business Configuration
SHIPROCKET_COMPANY_NAME=Your Company Name
SHIPROCKET_DEFAULT_LENGTH=10  # cm
SHIPROCKET_DEFAULT_WIDTH=10   # cm
SHIPROCKET_DEFAULT_HEIGHT=10  # cm
SHIPROCKET_DEFAULT_WEIGHT=0.5 # kg

# Feature Flags
SHIPROCKET_ENABLE_COD=true
SHIPROCKET_ENABLE_INTERNATIONAL=true
SHIPROCKET_ENABLE_INSURANCE=true
SHIPROCKET_ENABLE_FRAGILE_SHIPPING=true
```

---

## 📝 **Step 3: Setup Pickup Locations**

### **Add Pickup Location via Dashboard:**
1. Login to Shiprocket Dashboard
2. Go to "Settings" → "Pickup Locations"
3. Click "Add Pickup Location"
4. Fill details:
   - Location Name (e.g., "Main Warehouse")
   - Complete Address
   - Contact Person
   - Phone Number
   - Email
   - Pickup Timings
   - Pin Code

### **Add Pickup Location via API:**
```python
# Example: Add pickup location via API
pickup_data = {
    "pickup_location": "main_warehouse",
    "name": "Your Business Name",
    "email": "<EMAIL>",
    "phone": "9876543210",
    "address": "123 Business Street",
    "address_2": "Near Metro Station",
    "city": "Mumbai",
    "state": "Maharashtra",
    "country": "India",
    "pin_code": "400001"
}
```

---

## 🧪 **Step 4: Test Configuration**

Create and run this test script:

```python
# test_shiprocket.py
import os
from dotenv import load_dotenv
from shiprocket_integration import ShiprocketAPI

load_dotenv()

def test_shiprocket_config():
    """Test Shiprocket configuration"""
    
    print("🚚 Shiprocket Configuration Test")
    print("=" * 40)
    
    # Check credentials
    email = os.getenv('SHIPROCKET_EMAIL')
    password = os.getenv('SHIPROCKET_PASSWORD')
    
    if not email or not password:
        print("❌ Shiprocket credentials not configured")
        return False
    
    print(f"✅ Email: {email}")
    print(f"✅ Password: {'*' * len(password)}")
    
    # Test API connection
    shiprocket = ShiprocketAPI()
    
    if shiprocket.authenticate():
        print("✅ Authentication successful")
        
        # Test rate calculation
        print("\n📦 Testing rate calculation...")
        rates = shiprocket.calculate_rates('400001', '110001', 1.0, cod=True)
        
        if 'error' not in rates:
            print("✅ Rate calculation working")
            print(f"Available couriers: {len(rates.get('data', {}).get('available_courier_companies', []))}")
        else:
            print(f"❌ Rate calculation failed: {rates['error']}")
        
        # Test pickup locations
        print("\n📍 Testing pickup locations...")
        locations = shiprocket.get_pickup_locations()
        
        if 'error' not in locations:
            print("✅ Pickup locations retrieved")
            pickup_count = len(locations.get('data', {}).get('shipping_address', []))
            print(f"Configured pickup locations: {pickup_count}")
        else:
            print(f"❌ Pickup locations failed: {locations['error']}")
        
        return True
    else:
        print("❌ Authentication failed")
        return False

if __name__ == "__main__":
    test_shiprocket_config()
```

---

## 🎯 **Step 5: API Usage Examples**

### **Rate Calculation:**
```python
# Calculate shipping rates
POST /api/fulfillment/rates
{
    "carrier": "shiprocket",
    "origin": {
        "pincode": "400001"
    },
    "destination": {
        "pincode": "110001"
    },
    "package": {
        "weight": 1.5,
        "length": 20,
        "width": 15,
        "height": 10,
        "declared_value": 2500
    },
    "cod": true
}
```

### **Create Shipment:**
```python
POST /api/fulfillment/shipments
{
    "carrier": "shiprocket",
    "order_id": "ORD-12345",
    "pickup_location": "main_warehouse",
    "customer": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "9876543210"
    },
    "shipping_address": {
        "address": "123 Customer Street",
        "city": "Delhi",
        "state": "Delhi",
        "pincode": "110001",
        "country": "India"
    },
    "items": [
        {
            "name": "Product Name",
            "sku": "SKU123",
            "quantity": 2,
            "price": 1250,
            "hsn": 123456
        }
    ],
    "package": {
        "weight": 1.5,
        "length": 20,
        "width": 15,
        "height": 10
    },
    "payment_method": "COD",
    "cod_amount": 2500
}
```

### **Track Shipment:**
```python
GET /api/fulfillment/shipments/{awb_number}/track

# Response includes:
{
    "awb_code": "AWB1********",
    "courier_company_id": 12,
    "courier_name": "Blue Dart",
    "current_status": "In Transit",
    "delivered_date": null,
    "destination": "Delhi",
    "origin": "Mumbai",
    "tracking_data": [
        {
            "date": "2025-07-14",
            "status": "Picked Up",
            "activity": "Package picked up from origin",
            "location": "Mumbai Hub"
        }
    ]
}
```

---

## 💰 **Step 6: Pricing & Billing**

### **Shiprocket Pricing Model:**
- **No Setup Fee**: Free account creation
- **No Monthly Fee**: Pay per shipment
- **Transparent Pricing**: Rate card available on dashboard
- **Volume Discounts**: Better rates for higher volumes

### **Typical Rates (Domestic):**
```
Surface (3-5 days):
- Within City: ₹30-50 for 500g
- Metro to Metro: ₹40-70 for 500g
- Metro to Non-Metro: ₹50-90 for 500g

Express (1-2 days):
- Within City: ₹50-80 for 500g
- Metro to Metro: ₹70-120 for 500g
- Metro to Non-Metro: ₹90-150 for 500g

Additional Charges:
- COD: ₹15-25 per shipment
- Fuel Surcharge: 10-15% of base rate
- GST: 18% on total charges
```

### **International Rates:**
- **Documents**: Starting ₹800 for 500g
- **Parcels**: Starting ₹1200 for 500g
- **Express International**: Starting ₹2000 for 500g

---

## 🌍 **Step 7: Available Courier Partners**

### **Domestic Partners:**
1. **Blue Dart** - Premium express
2. **FedEx** - International & domestic express
3. **Delhivery** - Pan-India coverage
4. **Ecom Express** - E-commerce specialist
5. **DTDC** - Cost-effective surface
6. **Xpressbees** - Last-mile delivery
7. **Shadowfax** - Same-day delivery
8. **Ekart** - Flipkart logistics
9. **Amazon Shipping** - Amazon logistics
10. **India Post** - Government postal service

### **International Partners:**
1. **DHL Express** - Worldwide express
2. **FedEx International** - Global coverage
3. **Aramex** - Middle East & Asia
4. **UPS** - Americas & Europe

---

## 🔍 **Step 8: Testing Checklist**

### **Before Going Live:**

- [ ] **Account Setup**: Shiprocket account created and verified
- [ ] **API Authentication**: Login credentials working
- [ ] **Pickup Locations**: At least one pickup location configured
- [ ] **Rate Calculation**: Test rates for different routes
- [ ] **Order Creation**: Create test orders successfully
- [ ] **AWB Generation**: Generate shipping labels
- [ ] **Tracking**: Verify tracking functionality
- [ ] **COD Testing**: Test COD orders if enabled
- [ ] **International**: Test international shipping if needed
- [ ] **Webhooks**: Configure status update webhooks
- [ ] **Billing**: Verify wallet balance and payment methods

### **Test Scenarios:**
1. **Mumbai to Delhi** (Metro to Metro)
2. **Mumbai to Tier-2 City** (Coverage test)
3. **COD vs Prepaid** (Payment method test)
4. **Different Weights** (0.5kg, 2kg, 5kg)
5. **International Shipment** (If enabled)
6. **Bulk Orders** (Multiple orders)

---

## 🚨 **Step 9: Production Deployment**

### **Pre-Production Checklist:**

1. **Update Environment:**
```bash
SHIPROCKET_SANDBOX=false
SHIPROCKET_EMAIL=your_production_email
SHIPROCKET_PASSWORD=your_production_password
```

2. **Complete KYC**: Ensure all documents verified
3. **Add Wallet Balance**: Minimum ₹1000 recommended
4. **Configure Webhooks**: For real-time status updates
5. **Setup Monitoring**: Track API performance and errors

---

## 📞 **Support & Resources**

### **Shiprocket Support:**
- **Customer Support**: +91-11-4084-5555
- **Email**: <EMAIL>
- **API Support**: <EMAIL>
- **Documentation**: https://apidocs.shiprocket.in/
- **Dashboard**: https://app.shiprocket.in/

### **Useful Resources:**
- **Rate Calculator**: Available in dashboard
- **Pin Code Checker**: Serviceability verification
- **Tracking Page**: Public tracking interface
- **API Postman Collection**: Available in docs

---

## ✅ **Quick Start Summary**

1. **Create Shiprocket Account**: https://www.shiprocket.in/
2. **Add to .env file**:
```bash
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password
SHIPROCKET_SANDBOX=true
```
3. **Configure Pickup Location**: In Shiprocket dashboard
4. **Test Integration**: Run test script
5. **Start Shipping**: Use Shiprocket APIs through fulfillment endpoints

Your Shiprocket integration provides access to 17+ courier partners through a single API! 🚚📦✅

---

## 🔧 **Step 10: Integration with Allora Backend**

### **Update Fulfillment Service:**

Your existing fulfillment endpoints will automatically support Shiprocket:

```python
# Rate calculation will include Shiprocket rates
GET /api/fulfillment/rates?carrier=shiprocket

# Create shipments through Shiprocket
POST /api/fulfillment/shipments
{
    "carrier": "shiprocket",
    "service_type": "surface",  # or "express"
    ...
}

# Track shipments
GET /api/fulfillment/shipments/{shipment_id}/track
```

### **Webhook Integration:**

Configure webhooks in Shiprocket dashboard:
- **Webhook URL**: `https://yourdomain.com/api/webhooks/shiprocket`
- **Events**: Order status updates, delivery confirmations
- **Authentication**: Use webhook secret for verification

---

*Last Updated: July 14, 2025*
*For the latest API documentation, visit: https://apidocs.shiprocket.in/*

# Blue Dart Shipping Configuration Guide for Allora Backend

**Target:** India Domestic Shipping  
**Carrier:** Blue Dart Express Limited  
**Integration Type:** REST API  
**Documentation:** [Blue Dart API Documentation](https://www.bluedart.com/web-api)

---

## 📋 **Overview**

Blue Dart is India's premier express air and integrated transportation company. This guide will help you configure Blue Dart shipping integration for domestic Indian deliveries in your Allora e-commerce backend.

### **Blue Dart Services Supported:**
- ✅ **Express Delivery** (Next day delivery)
- ✅ **Ground Service** (2-3 days delivery)
- ✅ **Cash on Delivery (COD)**
- ✅ **Prepaid Shipments**
- ✅ **Real-time Tracking**
- ✅ **Rate Calculation**
- ✅ **Pickup Scheduling**

---

## 🚀 **Step 1: Get Blue Dart API Credentials**

### **For Development/Testing:**

#### **Option A: Use Demo Credentials (Immediate Testing)**
```bash
# Add these to your .env file for immediate testing
BLUE_DART_API_KEY=demo_key
BLUE_DART_USERNAME=demo_user
BLUE_DART_PASSWORD=demo_pass
BLUE_DART_LICENSE_KEY=demo_license
BLUE_DART_SANDBOX=true
```

#### **Option B: Get Real Test Credentials**

1. **Visit Blue Dart Developer Portal:**
   - Go to: https://www.bluedart.com/web-api
   - Click on "API Documentation" or "Developer Access"

2. **Register for API Access:**
   - Fill out the registration form
   - Provide business details
   - Request sandbox/test environment access

3. **Required Information:**
   - Company Name
   - Contact Person
   - Email Address
   - Phone Number
   - Business Type
   - Expected Monthly Volume

4. **You'll Receive:**
   - API Key
   - Username
   - Password
   - License Key
   - Sandbox URL
   - Documentation Access

### **For Production:**

1. **Contact Blue Dart Sales:**
   - Call: 1860-233-1234
   - Email: <EMAIL>
   - Visit nearest Blue Dart office

2. **Complete KYC Process:**
   - Business Registration Certificate
   - GST Certificate
   - PAN Card
   - Address Proof
   - Bank Details

3. **Sign Service Agreement:**
   - Rate card negotiation
   - Service level agreements
   - Payment terms
   - Credit facility (if required)

---

## 🔧 **Step 2: Configure Environment Variables**

Add these variables to your `.env` file:

```bash
# ===========================================
# BLUE DART SHIPPING CONFIGURATION
# ===========================================

# Blue Dart API Credentials
BLUE_DART_API_KEY=your_api_key_here
BLUE_DART_USERNAME=your_username_here
BLUE_DART_PASSWORD=your_password_here
BLUE_DART_LICENSE_KEY=your_license_key_here

# Environment Settings
BLUE_DART_SANDBOX=true  # Set to false for production
BLUE_DART_BASE_URL=https://apigateway-sandbox.bluedart.com  # Sandbox URL

# Service Configuration
BLUE_DART_DEFAULT_SERVICE=Express  # Express, Ground, or Premium
BLUE_DART_COD_ENABLED=true
BLUE_DART_PICKUP_ENABLED=true

# Business Details (Required for shipments)
BLUE_DART_CUSTOMER_CODE=your_customer_code
BLUE_DART_ACCOUNT_NUMBER=your_account_number
BLUE_DART_SUBACCOUNT=your_subaccount_code

# Default Pickup Address
BLUE_DART_PICKUP_NAME=Your Business Name
BLUE_DART_PICKUP_ADDRESS=Your Business Address
BLUE_DART_PICKUP_CITY=Your City
BLUE_DART_PICKUP_STATE=Your State
BLUE_DART_PICKUP_PINCODE=Your Pincode
BLUE_DART_PICKUP_PHONE=Your Phone Number
BLUE_DART_PICKUP_EMAIL=<EMAIL>
```

---

## 📝 **Step 3: Update Configuration**

### **Production URLs:**
```bash
# For Production Environment
BLUE_DART_SANDBOX=false
BLUE_DART_BASE_URL=https://apigateway.bluedart.com
```

### **Service Types Available:**
```bash
# Express Service (Next Day)
BLUE_DART_EXPRESS_CODE=A

# Ground Service (2-3 Days)
BLUE_DART_GROUND_CODE=E

# Premium Service (Same Day in select cities)
BLUE_DART_PREMIUM_CODE=P
```

---

## 🧪 **Step 4: Test Configuration**

Create and run this test script:

```python
# test_blue_dart.py
import os
from dotenv import load_dotenv

load_dotenv()

def test_blue_dart_config():
    """Test Blue Dart configuration"""
    
    print("🚚 Blue Dart Configuration Test")
    print("=" * 40)
    
    # Check required credentials
    required_vars = [
        'BLUE_DART_API_KEY',
        'BLUE_DART_USERNAME', 
        'BLUE_DART_PASSWORD',
        'BLUE_DART_LICENSE_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            masked = value[:4] + '*' * (len(value) - 4) if len(value) > 4 else '*' * len(value)
            print(f"✅ {var}: {masked}")
        else:
            print(f"❌ {var}: Not configured")
            missing_vars.append(var)
    
    # Check environment settings
    sandbox = os.getenv('BLUE_DART_SANDBOX', 'true').lower() == 'true'
    print(f"🔧 Sandbox Mode: {'Enabled' if sandbox else 'Disabled'}")
    
    if missing_vars:
        print(f"\n⚠️  Missing variables: {', '.join(missing_vars)}")
        return False
    else:
        print(f"\n✅ Blue Dart configuration is complete!")
        return True

if __name__ == "__main__":
    test_blue_dart_config()
```

Run the test:
```bash
python test_blue_dart.py
```

---

## 🎯 **Step 5: API Usage Examples**

### **Rate Calculation:**
```python
# Example API call for rate calculation
POST /api/fulfillment/rates
{
    "carrier": "blue_dart",
    "service_type": "express",
    "origin": {
        "name": "Your Store",
        "address": "123 Business Street",
        "city": "Mumbai",
        "state": "Maharashtra", 
        "postal_code": "400001",
        "country": "IN"
    },
    "destination": {
        "name": "Customer Name",
        "address": "456 Customer Street",
        "city": "Delhi",
        "state": "Delhi",
        "postal_code": "110001", 
        "country": "IN"
    },
    "package": {
        "weight": 1.5,  # in KG
        "length": 20,   # in CM
        "width": 15,    # in CM  
        "height": 10,   # in CM
        "declared_value": 2500  # in INR
    }
}
```

### **Create Shipment:**
```python
POST /api/fulfillment/shipments
{
    "carrier": "blue_dart",
    "service_type": "express",
    "order_id": 12345,
    "payment_mode": "prepaid",  # or "cod"
    "cod_amount": 0,  # Set amount if COD
    "shipper": {
        "name": "Your Business Name",
        "address": "Your Business Address",
        "city": "Mumbai",
        "state": "Maharashtra",
        "postal_code": "400001",
        "phone": "9876543210",
        "email": "<EMAIL>"
    },
    "consignee": {
        "name": "Customer Name", 
        "address": "Customer Address",
        "city": "Delhi",
        "state": "Delhi",
        "postal_code": "110001",
        "phone": "9876543211",
        "email": "<EMAIL>"
    },
    "package": {
        "weight": 1.5,
        "dimensions": "20x15x10",
        "declared_value": 2500,
        "description": "Electronics Item"
    }
}
```

### **Track Shipment:**
```python
GET /api/fulfillment/shipments/{shipment_id}/track

# Response will include:
{
    "tracking_number": "BD123456789",
    "status": "in_transit",
    "current_location": "Delhi Hub",
    "estimated_delivery": "2025-07-15",
    "tracking_events": [
        {
            "timestamp": "2025-07-14T10:30:00Z",
            "status": "picked_up",
            "location": "Mumbai Hub",
            "description": "Package picked up"
        }
    ]
}
```

---

## 📍 **Step 6: Service Coverage**

### **Blue Dart Coverage Areas:**

#### **Express Service (Next Day):**
- All major cities (Mumbai, Delhi, Bangalore, Chennai, Kolkata, Hyderabad, Pune, Ahmedabad)
- 35,000+ pin codes across India
- Cut-off time: Usually 6 PM for next day delivery

#### **Ground Service (2-3 Days):**
- Pan-India coverage
- 220,000+ pin codes
- Cost-effective for non-urgent deliveries

#### **Cash on Delivery:**
- Available in 19,000+ pin codes
- COD charges: ₹25-50 per shipment
- Maximum COD amount: ₹50,000

---

## 💰 **Step 7: Pricing Structure**

### **Typical Rate Structure:**
```
Express Service:
- Within City: ₹60-80 for 500g
- Metro to Metro: ₹80-120 for 500g  
- Metro to Non-Metro: ₹100-150 for 500g

Ground Service:
- Within City: ₹40-60 for 500g
- Metro to Metro: ₹60-90 for 500g
- Metro to Non-Metro: ₹80-120 for 500g

Additional Charges:
- COD: ₹25-50 per shipment
- Fuel Surcharge: 15-20% of base rate
- Service Tax: 18% GST
```

*Note: Actual rates depend on your negotiated contract with Blue Dart*

---

## 🔍 **Step 8: Testing Checklist**

### **Before Going Live:**

- [ ] **Credentials Verified**: All API credentials working
- [ ] **Rate Calculation**: Test rate API with different origins/destinations  
- [ ] **Shipment Creation**: Create test shipments successfully
- [ ] **Tracking**: Verify tracking functionality
- [ ] **COD Testing**: Test COD shipments if enabled
- [ ] **Pickup Scheduling**: Test pickup request functionality
- [ ] **Error Handling**: Test invalid requests and error responses
- [ ] **Webhook Setup**: Configure tracking update webhooks
- [ ] **Production Switch**: Ready to switch from sandbox to production

### **Test Scenarios:**
1. **Mumbai to Delhi** (Metro to Metro)
2. **Mumbai to Pune** (Within State)  
3. **Delhi to Bangalore** (Cross Country)
4. **Metro to Tier-2 City** (Coverage test)
5. **COD Shipment** (If enabled)
6. **Express vs Ground** (Service comparison)

---

## 🚨 **Step 9: Production Deployment**

### **Pre-Production Checklist:**

1. **Update Environment Variables:**
```bash
BLUE_DART_SANDBOX=false
BLUE_DART_BASE_URL=https://apigateway.bluedart.com
```

2. **Verify Production Credentials:**
   - Test with small volume first
   - Verify rate calculations match contract
   - Test all service types

3. **Setup Monitoring:**
   - API response time monitoring
   - Error rate tracking  
   - Failed shipment alerts

4. **Configure Webhooks:**
   - Tracking update notifications
   - Delivery confirmation
   - Exception handling

---

## 📞 **Support & Troubleshooting**

### **Blue Dart Support:**
- **Customer Care**: 1860-233-1234
- **Email**: <EMAIL>
- **API Support**: <EMAIL>
- **Business Hours**: 9 AM - 6 PM (Mon-Sat)

### **Common Issues:**

1. **Authentication Failed:**
   - Verify API credentials
   - Check license key validity
   - Ensure account is active

2. **Invalid Pin Code:**
   - Use Blue Dart pin code checker
   - Verify serviceability 
   - Check service type availability

3. **Rate Calculation Errors:**
   - Verify package dimensions
   - Check weight limits
   - Validate addresses

---

## ✅ **Quick Start Summary**

1. **Add to .env file:**
```bash
BLUE_DART_API_KEY=demo_key
BLUE_DART_USERNAME=demo_user
BLUE_DART_PASSWORD=demo_pass  
BLUE_DART_LICENSE_KEY=demo_license
BLUE_DART_SANDBOX=true
```

2. **Test configuration:**
```bash
python test_blue_dart.py
```

3. **Start using Blue Dart APIs** through your fulfillment endpoints!

Your Blue Dart integration is now ready for India domestic shipping! 🚚🇮🇳✅

---

*Last Updated: July 14, 2025*  
*For the latest API documentation, visit: https://www.bluedart.com/web-api*

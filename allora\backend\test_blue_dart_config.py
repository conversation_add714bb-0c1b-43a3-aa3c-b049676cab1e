#!/usr/bin/env python3
"""
Blue Dart Configuration Test Script
==================================

Test script specifically for Blue Dart shipping carrier configuration.
"""

import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def test_result(test_name, status, message=""):
    """Print test result"""
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name:<40} [{status}]")
    if message:
        print(f"   {message}")

def test_blue_dart_credentials():
    """Test Blue Dart API credentials"""
    print_header("BLUE DART CREDENTIALS TEST")
    
    # Required credentials
    credentials = {
        'BLUE_DART_API_KEY': 'API Key for authentication',
        'BLUE_DART_USERNAME': 'Username for Blue Dart account',
        'BLUE_DART_PASSWORD': 'Password for Blue Dart account',
        'BLUE_DART_LICENSE_KEY': 'License key for API access'
    }
    
    all_present = True
    
    for var, description in credentials.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            masked_value = value[:4] + '*' * (len(value) - 4) if len(value) > 4 else '*' * len(value)
            test_result(var, "PASS", f"Value: {masked_value}")
        else:
            test_result(var, "FAIL", f"Missing: {description}")
            all_present = False
    
    return all_present

def test_blue_dart_configuration():
    """Test Blue Dart configuration settings"""
    print_header("BLUE DART CONFIGURATION TEST")
    
    # Environment settings
    sandbox = os.getenv('BLUE_DART_SANDBOX', 'true').lower() == 'true'
    base_url = os.getenv('BLUE_DART_BASE_URL', 'https://apigateway-sandbox.bluedart.com')
    
    test_result("Sandbox Mode", "PASS", f"Enabled: {sandbox}")
    test_result("Base URL", "PASS", f"URL: {base_url}")
    
    # Service configuration
    default_service = os.getenv('BLUE_DART_DEFAULT_SERVICE', 'Express')
    cod_enabled = os.getenv('BLUE_DART_COD_ENABLED', 'true').lower() == 'true'
    pickup_enabled = os.getenv('BLUE_DART_PICKUP_ENABLED', 'true').lower() == 'true'
    
    test_result("Default Service", "PASS", f"Service: {default_service}")
    test_result("COD Enabled", "PASS" if cod_enabled else "INFO", f"COD: {cod_enabled}")
    test_result("Pickup Enabled", "PASS" if pickup_enabled else "INFO", f"Pickup: {pickup_enabled}")
    
    return True

def test_business_details():
    """Test business details configuration"""
    print_header("BUSINESS DETAILS TEST")
    
    business_vars = {
        'BLUE_DART_CUSTOMER_CODE': 'Customer code from Blue Dart',
        'BLUE_DART_ACCOUNT_NUMBER': 'Account number for billing',
        'BLUE_DART_PICKUP_NAME': 'Business name for pickup',
        'BLUE_DART_PICKUP_ADDRESS': 'Pickup address',
        'BLUE_DART_PICKUP_CITY': 'Pickup city',
        'BLUE_DART_PICKUP_STATE': 'Pickup state',
        'BLUE_DART_PICKUP_PINCODE': 'Pickup pincode',
        'BLUE_DART_PICKUP_PHONE': 'Pickup contact number'
    }
    
    configured_count = 0
    
    for var, description in business_vars.items():
        value = os.getenv(var)
        if value:
            # Show partial value for address fields
            if 'ADDRESS' in var or 'NAME' in var:
                display_value = value[:20] + '...' if len(value) > 20 else value
            else:
                display_value = value
            test_result(var, "PASS", f"Value: {display_value}")
            configured_count += 1
        else:
            test_result(var, "WARN", f"Optional: {description}")
    
    if configured_count >= 6:
        test_result("Business Setup", "PASS", f"{configured_count}/8 fields configured")
    else:
        test_result("Business Setup", "WARN", f"Only {configured_count}/8 fields configured")
    
    return configured_count >= 4  # At least basic info should be present

def generate_sample_config():
    """Generate sample configuration for Blue Dart"""
    print_header("SAMPLE CONFIGURATION")
    
    sample_config = """
# Add these lines to your .env file for Blue Dart configuration:

# ===========================================
# BLUE DART SHIPPING CONFIGURATION
# ===========================================

# API Credentials (Get from Blue Dart)
BLUE_DART_API_KEY=your_api_key_here
BLUE_DART_USERNAME=your_username_here
BLUE_DART_PASSWORD=your_password_here
BLUE_DART_LICENSE_KEY=your_license_key_here

# Environment Settings
BLUE_DART_SANDBOX=true
BLUE_DART_BASE_URL=https://apigateway-sandbox.bluedart.com

# Service Configuration
BLUE_DART_DEFAULT_SERVICE=Express
BLUE_DART_COD_ENABLED=true
BLUE_DART_PICKUP_ENABLED=true

# Business Details (Optional but recommended)
BLUE_DART_CUSTOMER_CODE=your_customer_code
BLUE_DART_ACCOUNT_NUMBER=your_account_number
BLUE_DART_PICKUP_NAME=Your Business Name
BLUE_DART_PICKUP_ADDRESS=Your Business Address
BLUE_DART_PICKUP_CITY=Your City
BLUE_DART_PICKUP_STATE=Your State
BLUE_DART_PICKUP_PINCODE=400001
BLUE_DART_PICKUP_PHONE=**********
BLUE_DART_PICKUP_EMAIL=<EMAIL>

# For immediate testing, you can use these demo values:
# BLUE_DART_API_KEY=demo_key
# BLUE_DART_USERNAME=demo_user
# BLUE_DART_PASSWORD=demo_pass
# BLUE_DART_LICENSE_KEY=demo_license
"""
    
    print(sample_config)
    
    # Save sample config to file
    with open('blue_dart_sample.env', 'w') as f:
        f.write(sample_config.strip())
    
    print(f"\n📄 Sample configuration saved to: blue_dart_sample.env")

def test_api_endpoints():
    """Test which API endpoints will be available"""
    print_header("AVAILABLE API ENDPOINTS")
    
    endpoints = [
        ("Rate Calculation", "POST /api/fulfillment/rates", "Calculate shipping rates"),
        ("Create Shipment", "POST /api/fulfillment/shipments", "Create new shipment"),
        ("Track Shipment", "GET /api/fulfillment/shipments/{id}/track", "Track shipment status"),
        ("Schedule Pickup", "POST /api/fulfillment/pickups", "Schedule package pickup"),
        ("Cancel Shipment", "DELETE /api/fulfillment/shipments/{id}", "Cancel shipment"),
        ("Get Rates Comparison", "POST /api/fulfillment/rates/compare", "Compare rates across carriers")
    ]
    
    for name, endpoint, description in endpoints:
        test_result(name, "PASS", f"{endpoint} - {description}")

def main():
    """Main test function"""
    print("🚚 Blue Dart Configuration Test for Allora Backend")
    print("=" * 60)
    
    # Run all tests
    credentials_ok = test_blue_dart_credentials()
    config_ok = test_blue_dart_configuration()
    business_ok = test_business_details()
    
    # Show available endpoints
    test_api_endpoints()
    
    # Generate sample config if needed
    if not credentials_ok:
        generate_sample_config()
    
    # Summary
    print_header("CONFIGURATION SUMMARY")
    
    if credentials_ok:
        print("✅ Blue Dart credentials are configured")
        print("✅ Configuration settings are valid")
        if business_ok:
            print("✅ Business details are configured")
        else:
            print("⚠️  Business details partially configured")
        print("\n🎉 Blue Dart integration is ready!")
        print("You can now use Blue Dart for shipping in your Allora backend.")
        
        # Next steps
        print("\n📋 Next Steps:")
        print("1. Test rate calculation: POST /api/fulfillment/rates")
        print("2. Create test shipment: POST /api/fulfillment/shipments")
        print("3. Test tracking: GET /api/fulfillment/shipments/{id}/track")
        print("4. Configure webhooks for real-time updates")
        
        return True
    else:
        print("❌ Blue Dart credentials are missing")
        print("⚠️  Please configure Blue Dart API credentials in your .env file")
        print("\n📋 Required Steps:")
        print("1. Get Blue Dart API credentials")
        print("2. Add credentials to .env file")
        print("3. Run this test again")
        print("4. Check blue_dart_sample.env for configuration template")
        
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
Shiprocket Configuration Test Script
===================================

Test script for Shiprocket shipping integration configuration.
"""

import os
import sys
from dotenv import load_dotenv
from shiprocket_integration import ShiprocketAPI

# Load environment variables
load_dotenv()

def print_header(title):
    """Print section header"""
    print(f"\n{'='*60}")
    print(f"{title.center(60)}")
    print(f"{'='*60}")

def test_result(test_name, status, message=""):
    """Print test result"""
    status_symbol = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
    print(f"{status_symbol} {test_name:<40} [{status}]")
    if message:
        print(f"   {message}")

def test_shiprocket_credentials():
    """Test Shiprocket credentials"""
    print_header("SHIPROCKET CREDENTIALS TEST")
    
    email = os.getenv('SHIPROCKET_EMAIL')
    password = os.getenv('SHIPROCKET_PASSWORD')
    
    if email:
        test_result("Email", "PASS", f"Email: {email}")
    else:
        test_result("Email", "FAIL", "SHIPROCKET_EMAIL not configured")
        return False
    
    if password:
        masked_password = '*' * len(password)
        test_result("Password", "PASS", f"Password: {masked_password}")
    else:
        test_result("Password", "FAIL", "SHIPROCKET_PASSWORD not configured")
        return False
    
    return True

def test_shiprocket_configuration():
    """Test Shiprocket configuration settings"""
    print_header("SHIPROCKET CONFIGURATION TEST")
    
    # Environment settings
    sandbox = os.getenv('SHIPROCKET_SANDBOX', 'true').lower() == 'true'
    base_url = os.getenv('SHIPROCKET_BASE_URL', 'https://apiv2.shiprocket.in/v1')
    staging_url = os.getenv('SHIPROCKET_STAGING_URL', 'https://staging-apiv2.shiprocket.in/v1')
    
    test_result("Sandbox Mode", "PASS", f"Enabled: {sandbox}")
    test_result("Base URL", "PASS", f"URL: {base_url}")
    test_result("Staging URL", "PASS", f"URL: {staging_url}")
    
    # Default settings
    pickup_location = os.getenv('SHIPROCKET_DEFAULT_PICKUP_LOCATION', 'primary')
    auto_awb = os.getenv('SHIPROCKET_AUTO_AWB', 'true').lower() == 'true'
    auto_pickup = os.getenv('SHIPROCKET_AUTO_PICKUP', 'true').lower() == 'true'
    
    test_result("Default Pickup Location", "PASS", f"Location: {pickup_location}")
    test_result("Auto AWB Generation", "PASS" if auto_awb else "INFO", f"Enabled: {auto_awb}")
    test_result("Auto Pickup Scheduling", "PASS" if auto_pickup else "INFO", f"Enabled: {auto_pickup}")
    
    return True

def test_shiprocket_api_connection():
    """Test Shiprocket API connection"""
    print_header("SHIPROCKET API CONNECTION TEST")
    
    try:
        shiprocket = ShiprocketAPI()
        
        # Test authentication
        if shiprocket.authenticate():
            test_result("API Authentication", "PASS", "Successfully authenticated")
            
            # Test rate calculation
            print("\n📦 Testing Rate Calculation:")
            rates = shiprocket.calculate_rates('400001', '110001', 1.0, cod=True, declared_value=1000)
            
            if 'error' not in rates:
                test_result("Rate Calculation", "PASS", "Rate calculation working")
                
                # Show available couriers
                data = rates.get('data', {})
                couriers = data.get('available_courier_companies', [])
                if couriers:
                    test_result("Available Couriers", "PASS", f"Found {len(couriers)} courier options")
                    
                    # Show top 3 couriers
                    for i, courier in enumerate(couriers[:3]):
                        courier_name = courier.get('courier_name', 'Unknown')
                        rate = courier.get('rate', 0)
                        cod_charges = courier.get('cod_charges', 0)
                        total = rate + cod_charges
                        test_result(f"  {courier_name}", "INFO", f"Rate: ₹{total} (Base: ₹{rate}, COD: ₹{cod_charges})")
                else:
                    test_result("Available Couriers", "WARN", "No couriers found for this route")
            else:
                test_result("Rate Calculation", "FAIL", f"Error: {rates['error']}")
            
            # Test pickup locations
            print("\n📍 Testing Pickup Locations:")
            locations = shiprocket.get_pickup_locations()
            
            if 'error' not in locations:
                pickup_data = locations.get('data', {})
                addresses = pickup_data.get('shipping_address', [])
                
                if addresses:
                    test_result("Pickup Locations", "PASS", f"Found {len(addresses)} pickup location(s)")
                    
                    # Show pickup locations
                    for addr in addresses:
                        location_name = addr.get('pickup_location', 'Unknown')
                        city = addr.get('city', 'Unknown')
                        pincode = addr.get('pin_code', 'Unknown')
                        test_result(f"  {location_name}", "INFO", f"{city} - {pincode}")
                else:
                    test_result("Pickup Locations", "WARN", "No pickup locations configured")
            else:
                test_result("Pickup Locations", "FAIL", f"Error: {locations['error']}")
            
            return True
        else:
            test_result("API Authentication", "FAIL", "Authentication failed - check credentials")
            return False
            
    except Exception as e:
        test_result("API Connection", "FAIL", f"Connection error: {str(e)}")
        return False

def test_feature_configuration():
    """Test feature configuration"""
    print_header("FEATURE CONFIGURATION TEST")
    
    # Feature flags
    cod_enabled = os.getenv('SHIPROCKET_ENABLE_COD', 'true').lower() == 'true'
    international_enabled = os.getenv('SHIPROCKET_ENABLE_INTERNATIONAL', 'true').lower() == 'true'
    insurance_enabled = os.getenv('SHIPROCKET_ENABLE_INSURANCE', 'true').lower() == 'true'
    fragile_enabled = os.getenv('SHIPROCKET_ENABLE_FRAGILE_SHIPPING', 'true').lower() == 'true'
    
    test_result("COD Support", "PASS" if cod_enabled else "INFO", f"Enabled: {cod_enabled}")
    test_result("International Shipping", "PASS" if international_enabled else "INFO", f"Enabled: {international_enabled}")
    test_result("Insurance", "PASS" if insurance_enabled else "INFO", f"Enabled: {insurance_enabled}")
    test_result("Fragile Shipping", "PASS" if fragile_enabled else "INFO", f"Enabled: {fragile_enabled}")
    
    # Default package dimensions
    length = os.getenv('SHIPROCKET_DEFAULT_LENGTH', '10')
    width = os.getenv('SHIPROCKET_DEFAULT_WIDTH', '10')
    height = os.getenv('SHIPROCKET_DEFAULT_HEIGHT', '10')
    weight = os.getenv('SHIPROCKET_DEFAULT_WEIGHT', '0.5')
    
    test_result("Default Package Size", "PASS", f"{length}x{width}x{height} cm, {weight} kg")
    
    return True

def generate_sample_config():
    """Generate sample configuration"""
    print_header("SAMPLE CONFIGURATION")
    
    sample_config = """
# Add these lines to your .env file for Shiprocket configuration:

# ===========================================
# SHIPROCKET SHIPPING CONFIGURATION
# ===========================================

# Account Credentials
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password

# Environment Settings
SHIPROCKET_SANDBOX=true
SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1
SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1

# Default Configuration
SHIPROCKET_DEFAULT_PICKUP_LOCATION=primary
SHIPROCKET_AUTO_AWB=true
SHIPROCKET_AUTO_PICKUP=true

# Feature Flags
SHIPROCKET_ENABLE_COD=true
SHIPROCKET_ENABLE_INTERNATIONAL=true
SHIPROCKET_ENABLE_INSURANCE=true
SHIPROCKET_ENABLE_FRAGILE_SHIPPING=true

# Default Package Dimensions
SHIPROCKET_DEFAULT_LENGTH=10
SHIPROCKET_DEFAULT_WIDTH=10
SHIPROCKET_DEFAULT_HEIGHT=10
SHIPROCKET_DEFAULT_WEIGHT=0.5

# For immediate testing, you can use demo values:
# SHIPROCKET_EMAIL=<EMAIL>
# SHIPROCKET_PASSWORD=demo_password
"""
    
    print(sample_config)
    
    # Save sample config to file
    with open('shiprocket_sample.env', 'w') as f:
        f.write(sample_config.strip())
    
    print(f"\n📄 Sample configuration saved to: shiprocket_sample.env")

def show_next_steps():
    """Show next steps for setup"""
    print_header("NEXT STEPS")
    
    steps = [
        "1. Create Shiprocket account at https://www.shiprocket.in/",
        "2. Complete email and phone verification",
        "3. Add pickup location in Shiprocket dashboard",
        "4. Update .env file with your credentials",
        "5. Run this test script again to verify",
        "6. Test rate calculation and order creation",
        "7. Configure webhooks for status updates",
        "8. Go live with production credentials"
    ]
    
    for step in steps:
        print(f"   {step}")

def main():
    """Main test function"""
    print("🚚 Shiprocket Configuration Test for Allora Backend")
    print("=" * 60)
    
    # Run all tests
    credentials_ok = test_shiprocket_credentials()
    config_ok = test_shiprocket_configuration()
    features_ok = test_feature_configuration()
    
    if credentials_ok:
        api_ok = test_shiprocket_api_connection()
    else:
        api_ok = False
        generate_sample_config()
    
    # Summary
    print_header("CONFIGURATION SUMMARY")
    
    if credentials_ok and api_ok:
        print("✅ Shiprocket credentials are configured and working")
        print("✅ API connection successful")
        print("✅ Rate calculation working")
        print("✅ Configuration is complete")
        print("\n🎉 Shiprocket integration is ready!")
        print("You can now use Shiprocket for shipping in your Allora backend.")
        
        print("\n📋 Available APIs:")
        print("   • POST /api/fulfillment/rates - Calculate shipping rates")
        print("   • POST /api/fulfillment/shipments - Create shipments")
        print("   • GET /api/fulfillment/shipments/{id}/track - Track shipments")
        print("   • POST /api/fulfillment/pickups - Schedule pickups")
        
        return True
    else:
        print("❌ Shiprocket configuration incomplete")
        if not credentials_ok:
            print("⚠️  Missing Shiprocket credentials")
        if not api_ok:
            print("⚠️  API connection failed")
        
        show_next_steps()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

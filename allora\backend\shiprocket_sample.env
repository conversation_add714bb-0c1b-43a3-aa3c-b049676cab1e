# Add these lines to your .env file for Shiprocket configuration:

# ===========================================
# SHIPROCKET SHIPPING CONFIGURATION
# ===========================================

# Account Credentials
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password

# Environment Settings
SHIPROCKET_SANDBOX=true
SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1
SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1

# Default Configuration
SHIPROCKET_DEFAULT_PICKUP_LOCATION=primary
SHIPROCKET_AUTO_AWB=true
SHIPROCKET_AUTO_PICKUP=true

# Feature Flags
SHIPROCKET_ENABLE_COD=true
SHIPROCKET_ENABLE_INTERNATIONAL=true
SHIPROCKET_ENABLE_INSURANCE=true
SHIPROCKET_ENABLE_FRAGILE_SHIPPING=true

# Default Package Dimensions
SHIPROCKET_DEFAULT_LENGTH=10
SHIPROCKET_DEFAULT_WIDTH=10
SHIPROCKET_DEFAULT_HEIGHT=10
SHIPROCKET_DEFAULT_WEIGHT=0.5

# For immediate testing, you can use demo values:
# SHIPROCKET_EMAIL=<EMAIL>
# SHIPROCKET_PASSWORD=demo_password
#!/usr/bin/env python3
"""
Shiprocket Quick Setup Script
============================

Interactive script to configure Shiprocket shipping integration.
"""

import os

def setup_shiprocket_credentials():
    """Interactive setup for Shiprocket credentials"""
    
    print("🚚 Shiprocket Quick Setup for Allora Backend")
    print("=" * 60)
    print("This script will help you configure Shiprocket shipping integration.")
    print("You'll need a Shiprocket account to get started.\n")
    
    # Check if user has account
    has_account = input("Do you have a Shiprocket account? (y/n): ").strip().lower()
    
    if has_account != 'y':
        print("\n📋 To create a Shiprocket account:")
        print("1. Visit: https://www.shiprocket.in/")
        print("2. Click 'Sign Up' and complete registration")
        print("3. Verify your email and phone number")
        print("4. Add at least one pickup location")
        print("5. Come back and run this script again")
        return False
    
    print("\n🔑 Enter your Shiprocket credentials:")
    
    # Collect credentials
    email = input("Shiprocket Email: ").strip()
    password = input("Shiprocket Password: ").strip()
    
    if not email or not password:
        print("❌ Email and password are required!")
        return False
    
    # Environment selection
    print("\n🔧 Environment Selection:")
    print("1. Sandbox/Testing (recommended for development)")
    print("2. Production (for live orders)")
    
    env_choice = input("Choose environment (1 or 2): ").strip()
    sandbox = env_choice != '2'
    
    # Feature configuration
    print("\n⚙️ Feature Configuration:")
    
    cod_enabled = input("Enable Cash on Delivery (COD)? (y/n): ").strip().lower() == 'y'
    international_enabled = input("Enable International Shipping? (y/n): ").strip().lower() == 'y'
    insurance_enabled = input("Enable Insurance? (y/n): ").strip().lower() == 'y'
    
    # Default package dimensions
    print("\n📦 Default Package Dimensions:")
    print("(These will be used when package dimensions are not specified)")
    
    length = input("Default Length (cm) [10]: ").strip() or "10"
    width = input("Default Width (cm) [10]: ").strip() or "10"
    height = input("Default Height (cm) [10]: ").strip() or "10"
    weight = input("Default Weight (kg) [0.5]: ").strip() or "0.5"
    
    # Generate configuration
    config_lines = [
        "\n# ===========================================",
        "# SHIPROCKET SHIPPING CONFIGURATION",
        "# ===========================================",
        "",
        "# Account Credentials",
        f"SHIPROCKET_EMAIL={email}",
        f"SHIPROCKET_PASSWORD={password}",
        "",
        "# Environment Settings",
        f"SHIPROCKET_SANDBOX={'true' if sandbox else 'false'}",
        "SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1",
        "SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1",
        "",
        "# Default Configuration",
        "SHIPROCKET_DEFAULT_PICKUP_LOCATION=primary",
        "SHIPROCKET_AUTO_AWB=true",
        "SHIPROCKET_AUTO_PICKUP=true",
        "",
        "# Feature Flags",
        f"SHIPROCKET_ENABLE_COD={'true' if cod_enabled else 'false'}",
        f"SHIPROCKET_ENABLE_INTERNATIONAL={'true' if international_enabled else 'false'}",
        f"SHIPROCKET_ENABLE_INSURANCE={'true' if insurance_enabled else 'false'}",
        "SHIPROCKET_ENABLE_FRAGILE_SHIPPING=true",
        "",
        "# Default Package Dimensions",
        f"SHIPROCKET_DEFAULT_LENGTH={length}",
        f"SHIPROCKET_DEFAULT_WIDTH={width}",
        f"SHIPROCKET_DEFAULT_HEIGHT={height}",
        f"SHIPROCKET_DEFAULT_WEIGHT={weight}",
        ""
    ]
    
    config_text = "\n".join(config_lines)
    
    # Show configuration
    print("\n" + "="*60)
    print("Generated Shiprocket Configuration:")
    print("="*60)
    print(config_text)
    
    # Ask to append to .env
    print("="*60)
    choice = input("Add this configuration to your .env file? (y/n): ").strip().lower()
    
    if choice == 'y':
        try:
            with open('.env', 'a') as f:
                f.write(config_text)
            print("✅ Configuration added to .env file!")
            
            # Test the configuration
            test_choice = input("\nTest the configuration now? (y/n): ").strip().lower()
            if test_choice == 'y':
                print("\n🧪 Testing Shiprocket configuration...")
                os.system("python test_shiprocket_config.py")
            
        except Exception as e:
            print(f"❌ Error writing to .env file: {e}")
            print("Please manually add the configuration to your .env file.")
    else:
        print("📄 Please manually add the above configuration to your .env file.")
    
    return True

def show_shiprocket_features():
    """Show Shiprocket features and benefits"""
    
    print("\n🚚 Shiprocket Features & Benefits:")
    print("=" * 50)
    
    features = [
        "✅ 17+ Courier Partners (Blue Dart, FedEx, Delhivery, etc.)",
        "✅ Single API for Multiple Carriers",
        "✅ Automatic Rate Comparison",
        "✅ Real-time Tracking",
        "✅ COD & Prepaid Support",
        "✅ International Shipping (220+ countries)",
        "✅ Pickup Scheduling",
        "✅ Return Management (RTO)",
        "✅ Weight Reconciliation",
        "✅ Dashboard & Analytics",
        "✅ Webhook Support",
        "✅ No Setup Fee",
        "✅ Pay per Shipment"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n📊 Typical Domestic Rates:")
    print("   • Surface (3-5 days): ₹30-90 for 500g")
    print("   • Express (1-2 days): ₹50-150 for 500g")
    print("   • COD Charges: ₹15-25 per shipment")
    print("   • International: Starting ₹800 for 500g")

def show_setup_checklist():
    """Show setup checklist"""
    
    print("\n📋 Shiprocket Setup Checklist:")
    print("=" * 50)
    
    checklist = [
        "1. ✅ Create Shiprocket account",
        "2. ✅ Verify email and phone",
        "3. ✅ Add pickup location(s)",
        "4. ✅ Configure .env variables",
        "5. ⏳ Test API connection",
        "6. ⏳ Test rate calculation",
        "7. ⏳ Create test shipment",
        "8. ⏳ Test tracking",
        "9. ⏳ Configure webhooks",
        "10. ⏳ Go live"
    ]
    
    for item in checklist:
        print(f"   {item}")

def main():
    """Main setup function"""
    
    print("🚚 Shiprocket Integration Setup")
    print("=" * 60)
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please make sure you're running this script from the backend directory.")
        return
    
    print("Choose an option:")
    print("1. Setup Shiprocket credentials")
    print("2. Show Shiprocket features")
    print("3. Show setup checklist")
    print("4. Test existing configuration")
    print("5. Exit")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == '1':
        if setup_shiprocket_credentials():
            print("\n🎉 Shiprocket setup complete!")
            print("Your Allora backend now supports Shiprocket shipping.")
            print("\n📋 Next Steps:")
            print("1. Test the configuration: python test_shiprocket_config.py")
            print("2. Add pickup locations in Shiprocket dashboard")
            print("3. Start using shipping APIs in your application")
    elif choice == '2':
        show_shiprocket_features()
    elif choice == '3':
        show_setup_checklist()
    elif choice == '4':
        print("\n🧪 Testing existing configuration...")
        os.system("python test_shiprocket_config.py")
    elif choice == '5':
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()

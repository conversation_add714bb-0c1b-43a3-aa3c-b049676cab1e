#!/usr/bin/env python3
"""
Blue Dart Business Details Setup Script
=======================================

Interactive script to configure Blue Dart business details.
"""

import os

def setup_business_details():
    """Interactive setup for Blue Dart business details"""
    
    print("🚚 Blue Dart Business Details Setup")
    print("=" * 50)
    print("This script will help you configure your business details for Blue Dart shipping.")
    print("These details will be used for pickup address and shipment creation.\n")
    
    # Collect business information
    business_details = {}
    
    print("📋 Business Information:")
    business_details['BLUE_DART_CUSTOMER_CODE'] = input("Customer Code (from Blue Dart): ").strip() or "DEMO_CUSTOMER"
    business_details['BLUE_DART_ACCOUNT_NUMBER'] = input("Account Number (from Blue Dart): ").strip() or "DEMO_ACCOUNT"
    
    print("\n📍 Pickup Address Information:")
    business_details['BLUE_DART_PICKUP_NAME'] = input("Business Name: ").strip() or "Demo Business"
    business_details['BLUE_DART_PICKUP_ADDRESS'] = input("Business Address: ").strip() or "123 Business Street, Business Area"
    business_details['BLUE_DART_PICKUP_CITY'] = input("City: ").strip() or "Mumbai"
    business_details['BLUE_DART_PICKUP_STATE'] = input("State: ").strip() or "Maharashtra"
    business_details['BLUE_DART_PICKUP_PINCODE'] = input("Pincode: ").strip() or "400001"
    business_details['BLUE_DART_PICKUP_PHONE'] = input("Phone Number: ").strip() or "**********"
    business_details['BLUE_DART_PICKUP_EMAIL'] = input("Email Address: ").strip() or "<EMAIL>"
    
    # Generate the configuration
    config_lines = [
        "\n# Blue Dart Business Details",
        "# =============================="
    ]
    
    for key, value in business_details.items():
        config_lines.append(f"{key}={value}")
    
    config_text = "\n".join(config_lines)
    
    print("\n" + "="*50)
    print("Generated Configuration:")
    print("="*50)
    print(config_text)
    
    # Ask if user wants to append to .env
    print("\n" + "="*50)
    choice = input("Do you want to append this to your .env file? (y/n): ").strip().lower()
    
    if choice == 'y':
        try:
            with open('.env', 'a') as f:
                f.write(config_text + "\n")
            print("✅ Configuration appended to .env file!")
            print("🔄 Please restart your application to load the new configuration.")
        except Exception as e:
            print(f"❌ Error writing to .env file: {e}")
            print("Please manually add the configuration to your .env file.")
    else:
        print("📄 Please manually add the above configuration to your .env file.")
    
    print("\n🎉 Blue Dart business setup complete!")
    print("You can now test the full Blue Dart integration.")

def show_sample_addresses():
    """Show sample Indian addresses for testing"""
    
    print("\n📍 Sample Indian Addresses for Testing:")
    print("=" * 50)
    
    samples = [
        {
            "name": "Mumbai Office",
            "address": "Andheri East, Near Metro Station",
            "city": "Mumbai",
            "state": "Maharashtra", 
            "pincode": "400069"
        },
        {
            "name": "Delhi Office",
            "address": "Connaught Place, Central Delhi",
            "city": "New Delhi",
            "state": "Delhi",
            "pincode": "110001"
        },
        {
            "name": "Bangalore Office", 
            "address": "Koramangala, 5th Block",
            "city": "Bangalore",
            "state": "Karnataka",
            "pincode": "560095"
        },
        {
            "name": "Chennai Office",
            "address": "T. Nagar, Near Bus Stand",
            "city": "Chennai", 
            "state": "Tamil Nadu",
            "pincode": "600017"
        }
    ]
    
    for i, addr in enumerate(samples, 1):
        print(f"\n{i}. {addr['name']}:")
        print(f"   Address: {addr['address']}")
        print(f"   City: {addr['city']}")
        print(f"   State: {addr['state']}")
        print(f"   Pincode: {addr['pincode']}")

def main():
    """Main setup function"""
    
    print("🚚 Blue Dart Business Configuration Setup")
    print("=" * 60)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please make sure you're running this script from the backend directory.")
        return
    
    print("Choose an option:")
    print("1. Setup business details interactively")
    print("2. Show sample addresses for testing")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        setup_business_details()
    elif choice == '2':
        show_sample_addresses()
    elif choice == '3':
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()

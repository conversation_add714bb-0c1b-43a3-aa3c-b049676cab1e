#!/usr/bin/env python3
"""
Shipping Carrier Configuration Test
==================================

Test script to validate shipping carrier configurations.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_shipping_carriers():
    """Test shipping carrier configurations"""
    
    print("🚚 Shipping Carrier Configuration Test")
    print("=" * 50)
    
    carriers = {
        'FedEx': {
            'api_key': os.getenv('FEDEX_API_KEY'),
            'secret_key': os.getenv('FEDEX_SECRET_KEY'),
            'account_number': os.getenv('FEDEX_ACCOUNT_NUMBER'),
            'sandbox': os.getenv('FEDEX_SANDBOX', 'true').lower() == 'true'
        },
        'UPS': {
            'api_key': os.getenv('UPS_API_KEY'),
            'username': os.getenv('UPS_USERNAME'),
            'password': os.getenv('UPS_PASSWORD'),
            'sandbox': os.getenv('UPS_SANDBOX', 'true').lower() == 'true'
        },
        'DHL': {
            'api_key': os.getenv('DHL_API_KEY'),
            'api_secret': os.getenv('DHL_API_SECRET'),
            'account_number': os.getenv('DHL_ACCOUNT_NUMBER'),
            'sandbox': os.getenv('DHL_SANDBOX', 'true').lower() == 'true'
        },
        'Blue Dart': {
            'api_key': os.getenv('BLUE_DART_API_KEY'),
            'username': os.getenv('BLUE_DART_USERNAME'),
            'password': os.getenv('BLUE_DART_PASSWORD'),
            'sandbox': os.getenv('BLUE_DART_SANDBOX', 'true').lower() == 'true'
        },
        'Delhivery': {
            'api_key': os.getenv('DELHIVERY_API_KEY'),
            'sandbox': os.getenv('DELHIVERY_SANDBOX', 'true').lower() == 'true'
        }
    }
    
    configured_carriers = []
    
    for carrier_name, config in carriers.items():
        print(f"\n{carrier_name}:")
        
        # Check if carrier is configured
        has_required_fields = False
        
        if carrier_name == 'FedEx':
            has_required_fields = all([config['api_key'], config['secret_key'], config['account_number']])
        elif carrier_name == 'UPS':
            has_required_fields = all([config['api_key'], config['username'], config['password']])
        elif carrier_name == 'DHL':
            has_required_fields = all([config['api_key'], config['api_secret'], config['account_number']])
        elif carrier_name == 'Blue Dart':
            has_required_fields = all([config['api_key'], config['username'], config['password']])
        elif carrier_name == 'Delhivery':
            has_required_fields = bool(config['api_key'])
        
        if has_required_fields:
            print(f"  ✅ Configured")
            print(f"  📦 Sandbox Mode: {'Yes' if config['sandbox'] else 'No'}")
            configured_carriers.append(carrier_name)
            
            # Show masked credentials
            for key, value in config.items():
                if value and key != 'sandbox':
                    masked_value = value[:4] + '*' * (len(value) - 4) if len(value) > 4 else '*' * len(value)
                    print(f"  🔑 {key}: {masked_value}")
        else:
            print(f"  ❌ Not configured")
    
    print(f"\n📊 Summary:")
    print(f"Configured Carriers: {len(configured_carriers)}")
    print(f"Available Carriers: {', '.join(configured_carriers) if configured_carriers else 'None'}")
    
    if configured_carriers:
        print(f"\n✅ Shipping functionality is ready!")
        print(f"You can now use these carriers for order fulfillment.")
    else:
        print(f"\n⚠️  No shipping carriers configured.")
        print(f"Add carrier credentials to .env file to enable shipping.")
    
    return len(configured_carriers) > 0

if __name__ == "__main__":
    test_shipping_carriers()

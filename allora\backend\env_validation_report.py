#!/usr/bin/env python3
"""
Environment Validation Report Generator for Allora Backend
==========================================================

This script generates a comprehensive report of the .env configuration
and creates recommendations for production deployment.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def generate_env_report():
    """Generate comprehensive environment validation report"""
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "environment": os.getenv('FLASK_ENV', 'development'),
        "validation_results": {},
        "security_assessment": {},
        "production_readiness": {},
        "recommendations": []
    }
    
    # 1. Core Configuration Validation
    core_config = {
        "SECRET_KEY": {
            "present": bool(os.getenv('SECRET_KEY')),
            "length": len(os.getenv('SECRET_KEY', '')),
            "secure": len(os.getenv('SECRET_KEY', '')) >= 32
        },
        "JWT_SECRET_KEY": {
            "present": bool(os.getenv('JWT_SECRET_KEY')),
            "length": len(os.getenv('JWT_SECRET_KEY', '')),
            "secure": len(os.getenv('JWT_SECRET_KEY', '')) >= 32
        },
        "DATABASE_URL": {
            "present": bool(os.getenv('DATABASE_URL')),
            "type": "mysql" if "mysql" in os.getenv('DATABASE_URL', '') else "other",
            "secure": "@localhost" not in os.getenv('DATABASE_URL', '') or os.getenv('FLASK_ENV') == 'development'
        },
        "FLASK_ENV": {
            "present": bool(os.getenv('FLASK_ENV')),
            "value": os.getenv('FLASK_ENV', 'development'),
            "valid": os.getenv('FLASK_ENV') in ['development', 'production', 'testing']
        }
    }
    
    report["validation_results"]["core_configuration"] = core_config
    
    # 2. Service Configuration
    services_config = {
        "redis": {
            "host": os.getenv('REDIS_HOST', 'localhost'),
            "port": os.getenv('REDIS_PORT', '6379'),
            "password_protected": bool(os.getenv('REDIS_PASSWORD')),
            "multiple_dbs": bool(os.getenv('SESSION_REDIS') and os.getenv('CACHE_REDIS'))
        },
        "elasticsearch": {
            "enabled": os.getenv('ELASTICSEARCH_ENABLED', 'true').lower() == 'true',
            "host": os.getenv('ELASTICSEARCH_HOST', 'localhost'),
            "port": os.getenv('ELASTICSEARCH_PORT', '9200'),
            "auth_configured": bool(os.getenv('ELASTICSEARCH_USERNAME'))
        },
        "email": {
            "server": os.getenv('MAIL_SERVER'),
            "port": os.getenv('MAIL_PORT'),
            "tls_enabled": os.getenv('MAIL_USE_TLS', 'true').lower() == 'true',
            "credentials_present": bool(os.getenv('MAIL_USERNAME') and os.getenv('MAIL_PASSWORD'))
        }
    }
    
    report["validation_results"]["services_configuration"] = services_config
    
    # 3. Security Assessment
    security_assessment = {
        "https_redirect": os.getenv('HTTPS_REDIRECT', 'false').lower() == 'true',
        "rate_limiting": os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true',
        "cors_configured": bool(os.getenv('CORS_ORIGINS')),
        "session_security": {
            "redis_sessions": bool(os.getenv('SESSION_REDIS')),
            "session_signing": os.getenv('SESSION_USE_SIGNER', 'true').lower() == 'true'
        },
        "oauth_security": {
            "google_configured": bool(os.getenv('GOOGLE_CLIENT_ID') and os.getenv('GOOGLE_CLIENT_SECRET')),
            "multiple_providers": bool(os.getenv('FACEBOOK_CLIENT_ID') or os.getenv('GITHUB_CLIENT_ID'))
        }
    }
    
    report["security_assessment"] = security_assessment
    
    # 4. Feature Configuration
    features_config = {
        "ml_recommendations": os.getenv('ENABLE_RECOMMENDATIONS', 'true').lower() == 'true',
        "analytics": os.getenv('ENABLE_ANALYTICS', 'true').lower() == 'true',
        "notifications": os.getenv('ENABLE_NOTIFICATIONS', 'true').lower() == 'true',
        "multi_vendor": os.getenv('ENABLE_MULTI_VENDOR', 'true').lower() == 'true',
        "inventory_sync": os.getenv('ENABLE_INVENTORY_SYNC', 'false').lower() == 'true'
    }
    
    report["validation_results"]["features_configuration"] = features_config
    
    # 5. External Integrations
    integrations = {
        "payment_gateways": {
            "razorpay": bool(os.getenv('RAZORPAY_KEY_ID') and os.getenv('RAZORPAY_KEY_SECRET'))
        },
        "sms_service": bool(os.getenv('SMS_API_KEY')),
        "monitoring": {
            "sentry": bool(os.getenv('SENTRY_DSN')),
            "new_relic": bool(os.getenv('NEW_RELIC_LICENSE_KEY'))
        },
        "shipping_carriers": {
            "fedex": bool(os.getenv('FEDEX_API_KEY')),
            "ups": bool(os.getenv('UPS_API_KEY')),
            "dhl": bool(os.getenv('DHL_API_KEY')),
            "blue_dart": bool(os.getenv('BLUE_DART_API_KEY')),
            "delhivery": bool(os.getenv('DELHIVERY_API_KEY'))
        }
    }
    
    report["validation_results"]["external_integrations"] = integrations
    
    # 6. Production Readiness Assessment
    flask_env = os.getenv('FLASK_ENV', 'development')
    production_ready = {
        "environment_set": flask_env == 'production',
        "debug_disabled": os.getenv('FLASK_DEBUG', 'true').lower() == 'false',
        "https_enabled": security_assessment["https_redirect"],
        "secure_secrets": core_config["SECRET_KEY"]["secure"] and core_config["JWT_SECRET_KEY"]["secure"],
        "monitoring_configured": integrations["monitoring"]["sentry"],
        "rate_limiting_enabled": security_assessment["rate_limiting"]
    }
    
    production_score = sum(production_ready.values()) / len(production_ready) * 100
    report["production_readiness"] = {
        "score": round(production_score, 1),
        "details": production_ready,
        "ready": production_score >= 80
    }
    
    # 7. Generate Recommendations
    recommendations = []
    
    if not core_config["SECRET_KEY"]["secure"]:
        recommendations.append({
            "priority": "HIGH",
            "category": "Security",
            "issue": "Secret key too short",
            "recommendation": "Generate a secret key with at least 32 characters"
        })
    
    if not core_config["JWT_SECRET_KEY"]["secure"]:
        recommendations.append({
            "priority": "HIGH", 
            "category": "Security",
            "issue": "JWT secret key too short",
            "recommendation": "Generate a JWT secret key with at least 32 characters"
        })
    
    if flask_env == 'production' and not security_assessment["https_redirect"]:
        recommendations.append({
            "priority": "HIGH",
            "category": "Security",
            "issue": "HTTPS not enforced in production",
            "recommendation": "Set HTTPS_REDIRECT=true for production deployment"
        })
    
    if not services_config["redis"]["password_protected"] and flask_env == 'production':
        recommendations.append({
            "priority": "MEDIUM",
            "category": "Security", 
            "issue": "Redis not password protected",
            "recommendation": "Configure REDIS_PASSWORD for production deployment"
        })
    
    if not services_config["elasticsearch"]["auth_configured"] and flask_env == 'production':
        recommendations.append({
            "priority": "MEDIUM",
            "category": "Security",
            "issue": "Elasticsearch authentication not configured",
            "recommendation": "Configure ELASTICSEARCH_USERNAME and ELASTICSEARCH_PASSWORD"
        })
    
    if not integrations["monitoring"]["sentry"]:
        recommendations.append({
            "priority": "MEDIUM",
            "category": "Monitoring",
            "issue": "Error monitoring not configured",
            "recommendation": "Configure Sentry DSN for error tracking"
        })
    
    if sum(integrations["shipping_carriers"].values()) == 0:
        recommendations.append({
            "priority": "LOW",
            "category": "Features",
            "issue": "No shipping carriers configured",
            "recommendation": "Configure at least one shipping carrier API for order fulfillment"
        })
    
    report["recommendations"] = recommendations
    
    return report

def print_report(report):
    """Print formatted report to console"""
    print("🔧 Allora Backend Environment Validation Report")
    print("=" * 60)
    print(f"Generated: {report['timestamp']}")
    print(f"Environment: {report['environment']}")
    print()
    
    # Production Readiness Score
    score = report["production_readiness"]["score"]
    ready = report["production_readiness"]["ready"]
    
    print(f"Production Readiness Score: {score}% {'✅' if ready else '⚠️'}")
    print()
    
    # Core Configuration
    print("Core Configuration:")
    core = report["validation_results"]["core_configuration"]
    for key, config in core.items():
        status = "✅" if config.get("present", False) and config.get("secure", True) else "❌"
        print(f"  {status} {key}")
    print()
    
    # Services
    print("Services Configuration:")
    services = report["validation_results"]["services_configuration"]
    for service, config in services.items():
        if isinstance(config, dict):
            enabled = config.get("enabled", True) if "enabled" in config else True
            status = "✅" if enabled else "⚠️"
            print(f"  {status} {service.title()}")
    print()
    
    # Features
    print("Features Enabled:")
    features = report["validation_results"]["features_configuration"]
    for feature, enabled in features.items():
        status = "✅" if enabled else "⚪"
        print(f"  {status} {feature.replace('_', ' ').title()}")
    print()
    
    # Recommendations
    if report["recommendations"]:
        print("Recommendations:")
        for rec in report["recommendations"]:
            priority_icon = "🔴" if rec["priority"] == "HIGH" else "🟡" if rec["priority"] == "MEDIUM" else "🔵"
            print(f"  {priority_icon} [{rec['priority']}] {rec['issue']}")
            print(f"     → {rec['recommendation']}")
        print()
    else:
        print("✅ No recommendations - configuration looks good!")
        print()

def save_report(report, filename="env_validation_report.json"):
    """Save report to JSON file"""
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"📄 Report saved to: {filename}")

def main():
    """Main function"""
    report = generate_env_report()
    print_report(report)
    save_report(report)
    
    # Return exit code based on production readiness
    return 0 if report["production_readiness"]["ready"] else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
